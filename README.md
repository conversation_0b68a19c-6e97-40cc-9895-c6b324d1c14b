# Konvery gitops repository

This repo includes all configurations ready for git ops on cloud.
Please follow README files in sub-directories for more information.

## Usage

* aws_ops
  * operations such as vpc, subnet, vpn, eks launch, etc
  * written in Terraform
* k8s_ops
  * operations on eks
  * written in yaml
* conf_ops
  * utilities for backend component configuration
  * written in Python & JSON

## Issues/TODOs

* Networking
  * There's no hosted vpn on AWS China
  * A customized VPN server is launched before DX connected
  * Automated peering and route table is configured
* Grafana, Loki & Prometheus
