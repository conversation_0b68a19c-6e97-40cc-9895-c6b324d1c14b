{"checks": {"active": {"concurrency": 10, "healthy": {"http_statuses": [200, 302], "interval": 1, "successes": 2}, "http_path": "/healthz", "timeout": 1, "type": "http", "unhealthy": {"http_failures": 5, "http_statuses": [429, 404, 500, 501, 502, 503, 504, 505], "interval": 1, "tcp_failures": 2, "timeouts": 3}}}, "labels": {"version": "v1", "env": "local"}, "keepalive_pool": {"idle_timeout": 60, "requests": 1000, "size": 320}, "name": "apidocs", "pass_host": "pass", "scheme": "http", "nodes": {"apidocs:8050": 1}, "timeout": {"connect": 6, "read": 6, "send": 6}, "type": "roundrobin"}