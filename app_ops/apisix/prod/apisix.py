#!/bin/python3

import io
import json
import sys
import logging
import argparse
import requests
import yaml

DEFAULT_ADMIN_HOST = "https://apisix-admin.rp.konvery.work"
DEFAULT_API_KEY = "qYm5AW0kK4FTFqlA"
ROUTE_URL = "/apisix/admin/routes"
UPSTREAM_URL = "/apisix/admin/upstreams"
GLOBAL_RULE_URL = "/apisix/admin/global_rules"
IDS_FILE = "ids.json"

logger = logging.getLogger(__name__)


def update_conf(host, key):
    header = {"X-API-KEY": key}

    with open(IDS_FILE) as conf_file:
        conf = json.load(conf_file)

    # update upstreams
    for upstream, id in conf["upstreams"].items():
        p_url = host + UPSTREAM_URL + "/" + id
        file_path = "upstreams" + "/" + upstream + ".json"

        with open(file_path, "rb") as f:
            logger.info(f"making request to {p_url}, with file {file_path}")
            try:
                response = requests.put(url=p_url, headers=header, data=f)
                response.raise_for_status()
            except requests.exceptions.HTTPError as e:
                logger.exception(" ERROR ".center(80, "-"))
                logger.exception(e)
            except requests.exceptions.RequestException as e:
                logger.exception(e)

    # update routes
    for route, id in conf["routes"].items():
        p_url = host + ROUTE_URL + "/" + id
        file_path = "routes" + "/" + route + ".json"

        with open(file_path, "rb") as f:
            logger.info(f"making request to {p_url}, with file {file_path}")
            try:
                response = requests.put(url=p_url, headers=header, data=f)
                response.raise_for_status()
            except requests.exceptions.HTTPError as e:
                logger.exception(" ERROR ".center(80, "-"))
                logger.exception(e)
            except requests.exceptions.RequestException as e:
                logger.exception(e)

    # update global rules
    for rule, id in conf["global_rules"].items():
        p_url = host + GLOBAL_RULE_URL + "/" + id
        file_path = "global_rules" + "/" + rule + ".yaml"

        with open(file_path, "rb") as f:
            logger.info(f"making request to {p_url}, with file {file_path}")
            buf = io.BytesIO(json.dumps(yaml.full_load(f)).encode())
            try:
                response = requests.put(url=p_url, headers=header, data=buf)
                response.raise_for_status()
            except requests.exceptions.HTTPError as e:
                logger.exception(" ERROR ".center(80, "-"))
                logger.exception(e)
            except requests.exceptions.RequestException as e:
                logger.exception(e)

    return


if __name__ == "__main__":
    # args: host
    ap = argparse.ArgumentParser()

    ap.add_argument("-u", "--url", default=DEFAULT_ADMIN_HOST, help="admin host url")
    ap.add_argument("-k", "--key", default=DEFAULT_API_KEY, help="api auth key")
    ap.add_argument(
        "-d", "--debug", action="store_true", help="debug flag, default: false"
    )
    args = ap.parse_args()
    # logger.warning(args)

    if args.debug:
        logging.basicConfig(level=logging.DEBUG)
    update_conf(args.url, args.key)

    # make request to admin api url
    logger.info("successfully make request to admin url")
