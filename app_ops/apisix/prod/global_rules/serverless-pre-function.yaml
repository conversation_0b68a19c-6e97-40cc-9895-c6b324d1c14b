plugins:
  serverless-pre-function:
    disable: false
    phase: before_proxy
    functions:
      - |-
        -- serverless plugin to do user authentication

        -- configurations
        local auth_url = 'http://iam:8000/v1/users/me2' -- authentication url
        local skip_path_prefix = '/iam/' -- do not process requests to iam
        local auth_hosts = {
            ['anno.d.konvery.com']=1,
        } -- do authentication for these hosts only
        -- headers will be cleared
        local header_user = 'x-md-User'
        local header_user_uid = 'x-md-User-Uid'

        local http     = require('resty.http')
        local core     = require('apisix.core')
        local json     = core.json
        local sleep    = core.sleep
        local ngx      = ngx
        local base64   = ngx.encode_base64
        local rawget   = rawget
        local rawset   = rawset
        local setmetatable = setmetatable
        local string   = string

        local function new_headers()
            local t = {}
            local lt = {}
            local _mt = {
                __index = function(t, k)
                    return rawget(lt, string.lower(k))
                end,
                __newindex = function(t, k, v)
                    rawset(t, k, v)
                    rawset(lt, string.lower(k), v)
                end,
            }
            return setmetatable(t, _mt)
        end

        -- timeout in ms
        local function http_req(method, uri, body, myheaders, timeout)
            if myheaders == nil then myheaders = new_headers() end

            local httpc = http.new()
            if timeout then
                httpc:set_timeout(timeout)
            end

            local params = {method = method, headers = myheaders, body = body, ssl_verify = false}
            local res, err = httpc:request_uri(uri, params)
            if err then
                core.log.error('FAIL REQUEST [ ',core.json.delay_encode(
                    {method = method, uri = uri, body = body, headers = myheaders}),
                    ' ] failed! res is nil, err:', err)
                return nil, err
            end

            return res
        end

        local function http_get(uri, myheaders, timeout)
            return http_req('GET', uri, nil, myheaders, timeout)
        end

        local function get_user_info(auth_token)
            local retry_max = 3
            local res
            local err
            local headers = new_headers()
            headers['Authorization'] = auth_token
            local timeout = 1000 * 1

            for i = 1, retry_max do
                res, err = http_get(auth_url, headers, timeout)
                if err then
                    break
                else
                    -- core.log.warn('get users/me request:', auth_url, ', status:', res.status,
                    --              ',body:', core.json.delay_encode(res.body))
                    if res.status < 500 then
                        break
                    else
                        core.log.info('request [curl -v ', auth_url, '] failed! status:', res.status)
                        if i < retry_max then
                            sleep(0.1)
                        end
                    end
                end
            end

            if err then
                core.log.error('fail request: ', auth_url, ', err:', err)
                return {
                    status = 500,
                    err = 'request to auth-server failed, err:' .. err
                }
            end

            if res.status == 401 then
                return {status = 401, raw = res.body}
            end
            if res.status == 404 then
                return {status = 401, raw = {code=401, reason='err-unauthorized', message='user not found'}}
            end
            if res.status ~= 200 then
                return {
                    status = 500,
                    err = 'request to auth-server failed, status:' .. res.status
                }
            end

            local user_info, err = json.decode(res.body)
            if err then
                local errmsg = 'authentication failed! parse response json failed!'
                core.log.error( 'json.decode(', res.body, ') failed! err:', err)
                return {status = 500, err = errmsg}
            else
                return {status = res.status, user_info = user_info}
            end
        end

        return function (conf, ctx)
            -- core.log.warn('enter jwt-auth rewrite')
            core.request.set_header(ctx, header_user, '')
            core.request.set_header(ctx, header_user_uid, '')

            local host = core.request.get_host(ctx)
            if not auth_hosts[host] then
                core.log.info('skip auth for host: ', host)
                return
            end

            local auth_token = ngx.var['cookie_JWT']
            if auth_token then
                auth_token = 'Bearer ' .. auth_token
                core.request.set_header(ctx, 'Authorization', auth_token)
                -- core.log.warn('set auth header: ', auth_token)
            end
            if core.string.has_prefix(ctx.var.request_uri, skip_path_prefix) then
                core.log.info('skip auth for uri: ', ctx.var.request_uri)
                return
            end
            if not auth_token then
                auth_token = core.request.header(ctx, 'authorization')
            end
            if not auth_token or not core.string.has_prefix(auth_token, 'Bearer') then
                return
            end

            local res = get_user_info(auth_token)
            -- core.log.warn('get user info returns: ', core.json.delay_encode(res))
            if res.status == 401 then
                core.response.set_header('Content-Type', 'application/json')
                return 401, res.raw
            end
            if res.status == 500 then
                return 500, {code=500, reason='err-server-error', message=res.err}
            end

            local user = core.json.encode(res.user_info)
            core.request.set_header(ctx, header_user, base64(user))
            core.request.set_header(ctx, 'Authorization', '')
            -- core.log.warn('hit jwt-auth rewrite:', user, base64(user))
        end
