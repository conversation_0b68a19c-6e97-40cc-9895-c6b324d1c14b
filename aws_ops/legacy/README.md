# ReadMe

!!!NOTE!!!
DONOT TOUCH ANYTHING OR RUN ANY SCRIPT IN THIS FOLDER!!!
NO TFSTATE IS CURRENTLY SAVED!!!

This part of operations mainly launch and configure following components:

* legacy-vpc / legacy-network
* peering to other vpcs, configure route tables
* vpn server

## Provision & de-provision infrastructure

### Apply any change

```shell
# Make sure you have correct dns to resolve the s3 url
# compare the result vs www.whatsmydns.net
dig xxx
./run.sh <plan|apply>
```

### Remove everything in this folder

```shell
./run.sh destroy
# make sure you clearly check the change before hitting "yes"
```

## VPN

* VPN server is required to access resources on AWS from office to `create` a internal network.
* However, AWS China don't support AWS VPN service. We are on our own!
* OTP is enabled. Please use Google Authenticator for first time registration.

### USER maintinence

```shell

# Add user
cd /etc/openvpn/easy-rsa
./easyrsa build-client-full ${USER} nopass

# Add google authenticator
google-authenticator
mv /root/.google_authenticator /etc/openvpn/${USER}.google_authenticator

# Generator ovpn profile
/etc/openvpn/easy-rsa/ovpn_gen.sh hexiaonan

```
