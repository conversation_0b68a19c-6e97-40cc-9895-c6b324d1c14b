provider "aws" {
  region = "cn-northwest-1"
}

data "aws_availability_zones" "available" {}

#
# legacy vpc
#
module "legacy_vpc" {
  source  = "terraform-aws-modules/vpc/aws"
  version = "3.13.0"

  name = var.legacy_vpc_name
  cidr = var.legacy_vpc_cidr

  azs = data.aws_availability_zones.available.names

  propagate_private_route_tables_vgw = true
  private_subnets                    = var.legacy_pri_subnets
  private_subnet_tags = {
    subnet_type = "private"
  }

  public_subnets = var.legacy_pub_subnets
  public_subnet_tags = {
    subnet_type = "public"
  }

  manage_default_route_table = true
  default_route_table_tags   = { DefaultRouteTable = true }

  enable_dns_hostnames = true
  enable_dns_support   = true

  # don't enable nat gateway unless it is necessary
  enable_nat_gateway = false
  # single_nat_gateway = true

  # Default security group - ingress/egress rules cleared to deny all
  manage_default_security_group = true
  # doesn't open any inbound traffic
  default_security_group_ingress = []
  # default allow all outbound traffic
  default_security_group_egress = [
    {
      from_port   = 0
      to_port     = 0
      protocol    = "-1"
      cidr_blocks = "0.0.0.0/0"
    }
  ]

  # VPC Flow Logs (Cloudwatch log group and IAM role will be created)
  enable_flow_log                                 = true
  create_flow_log_cloudwatch_log_group            = true
  create_flow_log_cloudwatch_iam_role             = true
  flow_log_max_aggregation_interval               = 600
  flow_log_cloudwatch_log_group_retention_in_days = var.legacy_flow_log_retention

  tags = local.tags
}

# safely ignore s3 endpoint at this point

data "aws_vpc" "peer_from_vpc" {
  filter {
    name   = "tag:profile"
    values = ["legacy"]
  }
  depends_on = [
    module.legacy_vpc
  ]
}

module "legacy_vpc_peering_module" {
  source = "./modules/peering"

  vpc_peer_from = data.aws_vpc.peer_from_vpc.id

  # public subnet share a same table
  vpc_peer_from_route_id = module.legacy_vpc.public_route_table_ids[0]
}

module "vpn_module" {
  source = "./modules/vpn"
}
