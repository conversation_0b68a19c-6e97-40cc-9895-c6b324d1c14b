data "aws_caller_identity" "current" {}

data "aws_vpcs" "vpc_peer_to" {
  filter {
    name   = "tag:profile"
    values = ["non-prod", "prod"]
  }
}

data "aws_vpc" "vpc_peer_to" {
  count = length(data.aws_vpcs.vpc_peer_to.ids)
  id    = tolist(data.aws_vpcs.vpc_peer_to.ids)[count.index]
}

data "aws_vpc" "vpc_peer_from" {
  id = var.vpc_peer_from
}

data "aws_route_tables" "rt_peer_to" {
  count  = length(data.aws_vpc.vpc_peer_to)
  vpc_id = data.aws_vpc.vpc_peer_to[count.index].id

  filter {
    name   = "tag:peerlegacy"
    values = ["true"]
  }
}

locals {
  # We need to generate a single casecade variable
  legacy_peer_from_routes = flatten([
    for element in data.aws_route_tables.rt_peer_to : [
      for route_tabld_id in element.ids : {
        route_table_id            = route_tabld_id
        destination_cidr_block    = data.aws_vpc.vpc_peer_from.cidr_block
        vpc_peering_connection_id = aws_vpc_peering_connection.legacy_peer_infraworkload[index(data.aws_route_tables.rt_peer_to, element)].id
      }
    ]
  ])
}

resource "aws_vpc_peering_connection" "legacy_peer_infraworkload" {
  count = length(data.aws_vpc.vpc_peer_to)

  peer_owner_id = data.aws_caller_identity.current.account_id
  peer_vpc_id   = data.aws_vpc.vpc_peer_to[count.index].id
  vpc_id        = var.vpc_peer_from
  auto_accept   = true

  tags = {
    Name   = "VPC Peering between legacy vpc and infra+workload vpc"
    legacy = "true"
  }
}

# only support legacy public subnet to other peering subnet
# legacy public subnet has a unique route table
resource "aws_route" "legacy_peer_to_route" {
  count = length(data.aws_vpc.vpc_peer_to)

  route_table_id            = var.vpc_peer_from_route_id
  destination_cidr_block    = data.aws_vpc.vpc_peer_to[count.index].cidr_block
  vpc_peering_connection_id = aws_vpc_peering_connection.legacy_peer_infraworkload[count.index].id
}

resource "aws_route" "peer_to_legacy_route" {
  count = length(local.legacy_peer_from_routes)

  route_table_id            = local.legacy_peer_from_routes[count.index].route_table_id
  destination_cidr_block    = local.legacy_peer_from_routes[count.index].destination_cidr_block
  vpc_peering_connection_id = local.legacy_peer_from_routes[count.index].vpc_peering_connection_id
}
