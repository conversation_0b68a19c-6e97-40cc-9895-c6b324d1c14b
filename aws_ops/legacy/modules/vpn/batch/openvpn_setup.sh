#!/bin/sh

# set -x
sudo make-cadir /etc/openvpn/easy-rsa

cd /etc/openvpn/easy-rsa
./easyrsa init-pki
./easyrsa build-ca

#
# CA key passphrase: DS8jX3zqlkOjU724
# Common Name: konvery-aws-vpn
#

./easyrsa build-server-full konvery-aws-vpn nopass
# Enter the CA key pass

./easyrsa gen-dh

sudo openvpn --genkey --secret ./pki/ta.key

# Client configuration settings
./easyrsa build-client-full chenxin nopass
# or
./easyrsa import-req chenxin.req
./easyrsa sign-req client chenxin

systemctl start openvpn@konvery-aws-vpn

iptables -t nat -A POSTROUTING -s **********/24 -o ens5 -j MASQUERADE


# TOTP

# /etc/pam.d/openvpn
# auth    required                        pam_google_authenticator.so