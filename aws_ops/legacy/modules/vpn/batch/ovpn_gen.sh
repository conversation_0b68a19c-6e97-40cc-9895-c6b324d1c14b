#!/bin/bash

# Path to client configuration files
BASE_CONFIG=/etc/openvpn/easy-rsa/pki/base.conf

PKI_PATH=/etc/openvpn/easy-rsa/pki
CERT_PATH=/etc/openvpn/easy-rsa/pki/issued
KEY_PATH=/etc/openvpn/easy-rsa/pki/private

OUTPUT_DIR=/etc/openvpn/easy-rsa/pki/ovpn

rm ${OUTPUT_DIR}/${1}.ovpn

# Adding a new user in linux
# echo Adding user ${1} to the linux system
# useradd ${1}

# Create user certificates
# echo Generating certificates for user ${1}
# ./easyrsa build-client-full ${1} nopass

echo "Generating ovpn file for user ${1} ..."
cat ${BASE_CONFIG} \
<(echo -e "<key>") \
${KEY_PATH}/${1}.key \
<(echo -e "</key>\n<cert>") \
${CERT_PATH}/${1}.crt \
<(echo -e "</cert>\n<ca>") \
${PKI_PATH}/ca.crt \
<(echo -e '</ca>\n<tls-auth>') \
${PKI_PATH}/ta.key \
<(echo -e '</tls-auth>') \
> ${OUTPUT_DIR}/${1}.ovpn

echo "${1} ovpn file generated successfully"
echo ${OUTPUT_DIR}/${1}.ovpn