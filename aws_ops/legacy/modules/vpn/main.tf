data "aws_vpc" "legacy_vpc" {
  filter {
    name   = "tag:Name"
    values = ["legacy-vpc"]
  }
}

# do we really need high availability? Not now!
data "aws_subnet" "legacy_vpn_sub" {
  filter {
    name   = "tag:Name"
    values = ["legacy-vpc-public-cn-northwest-1a"]
  }
}

data "aws_ami" "legacy_vpn_ami" {
  most_recent = true

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }

  filter {
    name   = "architecture"
    values = [var.vpn_ins_arch]
  }

  filter {
    name   = "name"
    values = [var.vpn_ami_name]
  }

  owners = var.vpn_ami_owner
}

data "aws_key_pair" "legacy_vpn_keypair" {
  filter {
    name   = "key-name"
    values = ["konvery-sre-key"]
  }
}

resource "aws_security_group" "legacy_vpn_sg" {
  description = "rules for vpn server"
  name        = "legacy_vpn_security_group"
  vpc_id      = data.aws_vpc.legacy_vpc.id

  ingress {
    description = "openvpn"
    from_port   = 2224
    to_port     = 2224
    protocol    = "udp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    description = "ssh"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["************/32", "**********/24"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = -1
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "aws-vpn-sg"
  }
}

# Launch ec2
resource "aws_instance" "legacy_vpn_ec2" {
  ami               = data.aws_ami.legacy_vpn_ami.id
  availability_zone = var.vpn_az
  instance_type     = var.vpn_ins_type
  key_name          = data.aws_key_pair.legacy_vpn_keypair.key_name
  subnet_id         = data.aws_subnet.legacy_vpn_sub.id
  source_dest_check = false

  root_block_device {
    volume_type = "gp3"
  }

  user_data = <<EOF
#!/bin/bash
echo "Copying the SSH of terraform host to the server ..."
echo -e "# terraform host\nssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCk1UVy6q6FLjF5qBs9qMeij18ekxzyz9jVDTsiKYJCvSCuPWAPJT0NCuB2XmGmgYCFozeEU7Hb90iD5Z0L3PhCxhZjQ2tsIGFYWNQ+vBWFyc9l/oPQJhpEE6wSPCP8XT8n72F8O/uwD4BbzjpIUA2I6ook0kNMRT5jfXI4VENveTT6Di2e8SrvYIJrU2/6LW7X2lrXIqC7JsR7NLblaUH1o2CiqggJDh3WtSKzBINL4RJqvne8WVmeqKcQfDGovnO3MVgNzzAqWMMjquSJELl5d2D9wOM+gShUZD1exEcHq9AL89Xd+i+aZ/9ig23vLnnucAIRw+e1W/YUwIicZLj3 xin@Xin-MBP-2021" >> /home/<USER>/.ssh/authorized_keys
EOF

  vpc_security_group_ids = [aws_security_group.legacy_vpn_sg.id]

  tags = {
    Name = "aws-vpn-ec2"
  }
}

resource "aws_eip" "legacy_vpn_eip" {
  instance = aws_instance.legacy_vpn_ec2.id
  vpc      = true
  tags = {
    Name = "aws-vpn-eip"
  }
}

# Postscript
resource "null_resource" "legacy_vpn_postscript" {
  # Provisioners
  connection {
    host        = aws_eip.legacy_vpn_eip.public_ip
    private_key = file("./modules/vpn/keys/tf.host.pem")
    type        = "ssh"
    user        = "ubuntu"
  }

  provisioner "file" {
    source      = "./modules/vpn/batch"
    destination = "/tmp/"
  }

  provisioner "remote-exec" {
    inline = [
      "chmod a+x /tmp/batch/pre_setup.sh",
      "sudo /tmp/batch/pre_setup.sh",
      "chmod a+x /tmp/batch/openvpn_setup.sh",
      "sudo /tmp/batch/openvpn_setup.sh",
    ]
  }

  depends_on = [
    aws_eip.legacy_vpn_eip
  ]
}
