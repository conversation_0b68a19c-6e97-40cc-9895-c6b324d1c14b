variable "vpn_ins_type" {
  type        = string
  description = "VPN instance type to use"
  default     = "t3a.nano"
}

variable "vpn_ins_arch" {
  type        = string
  description = "Instance architect"
  # arm64 not working
  default = "x86_64"
}

variable "vpn_ami_name" {
  type        = string
  description = "AMI Name"
  # default     = "al2022-ami-*-arm64"
  default = "ubuntu/images/hvm-ssd/ubuntu-focal*"
}

variable "vpn_ami_owner" {
  type        = list(string)
  description = "AMI owners / owner alias"
  default     = ["amazon", "837727238323"]
}

variable "vpn_az" {
  type        = string
  description = "Availability Zone to use"
  default     = "cn-northwest-1a"
}
