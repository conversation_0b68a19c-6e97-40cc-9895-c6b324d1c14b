#!/bin/sh

# $1: plan, apply

# Lint clean
terraform fmt

case "$1" in
    "plan")
        terraform init --backend-config="key=tfstate/legacy-all" -reconfigure
        terraform plan --var-file input.tfvars;;
    "apply")
        terraform init --backend-config="key=tfstate/legacy-all" -reconfigure
        terraform apply --var-file input.tfvars;;
    "refresh")
        terraform init --backend-config="key=tfstate/legacy-all" -reconfigure
        terraform apply --var-file input.tfvars --refresh-only;;
    "destroy")
        terraform destroy --var-file input.tfvars;;
    *)
        echo "Usage ./run <plan|apply|destroy>"
        exit 1;;
esac