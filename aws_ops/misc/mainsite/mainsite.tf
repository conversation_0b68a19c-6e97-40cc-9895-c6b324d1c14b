data "aws_ami" "mainsite_vpn_ami" {
  most_recent = true

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }

  filter {
    name   = "architecture"
    values = ["arm64"]
  }

  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd/ubuntu-focal*"]
  }

  owners = ["amazon", "837727238323"]
}

data "aws_key_pair" "mainsite_vpn_keypair" {
  filter {
    name   = "key-name"
    values = ["konvery-sre-key"]
  }
}

data "aws_vpc" "mainsite_vpc" {
  filter {
    name   = "tag:profile"
    values = ["legacy"]
  }
}

data "aws_subnet" "mainsite_subnet" {
  filter {
    name   = "tag:Name"
    values = ["legacy-vpc-public-cn-northwest-1a"]
  }
}

resource "aws_security_group" "mainsite_vpn_sg" {
  description = "rules for mainsite server"
  name        = "mainsite_ec2_security_group"
  vpc_id      = data.aws_vpc.mainsite_vpc.id

  ingress {
    description = "http"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    description = "https"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    description = "ssh"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["************/32", "************/32"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = -1
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "aws-mainsite-sg"
  }
}

# Launch ec2
resource "aws_instance" "mainsite_vpn_ec2" {
  ami               = data.aws_ami.mainsite_vpn_ami.id
  availability_zone = data.aws_subnet.mainsite_subnet.availability_zone
  instance_type     = "t4g.nano"
  key_name          = data.aws_key_pair.mainsite_vpn_keypair.key_name
  subnet_id         = data.aws_subnet.mainsite_subnet.id

  root_block_device {
    volume_type = "gp3"
  }

  user_data = <<EOF
#!/bin/bash
echo "Copying the SSH of terraform host to the server ..."
echo -e "# terraform host\nssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCk1UVy6q6FLjF5qBs9qMeij18ekxzyz9jVDTsiKYJCvSCuPWAPJT0NCuB2XmGmgYCFozeEU7Hb90iD5Z0L3PhCxhZjQ2tsIGFYWNQ+vBWFyc9l/oPQJhpEE6wSPCP8XT8n72F8O/uwD4BbzjpIUA2I6ook0kNMRT5jfXI4VENveTT6Di2e8SrvYIJrU2/6LW7X2lrXIqC7JsR7NLblaUH1o2CiqggJDh3WtSKzBINL4RJqvne8WVmeqKcQfDGovnO3MVgNzzAqWMMjquSJELl5d2D9wOM+gShUZD1exEcHq9AL89Xd+i+aZ/9ig23vLnnucAIRw+e1W/YUwIicZLj3 xin@Xin-MBP-2021" >> /home/<USER>/.ssh/authorized_keys
EOF

  vpc_security_group_ids = [aws_security_group.mainsite_vpn_sg.id]

  tags = {
    Name = "aws-mainsite-ec2"
  }
}

resource "aws_eip" "mainsite_vpn_eip" {
  instance = aws_instance.mainsite_vpn_ec2.id
  vpc      = true
  tags = {
    Name = "aws-mainsite-eip"
  }
}
