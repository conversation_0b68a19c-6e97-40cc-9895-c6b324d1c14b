data "aws_caller_identity" "current" {}

data "aws_vpc" "vpc_peer_from" {
  filter {
    name   = "tag:Name"
    values = ["prod-infra"]
  }
}

data "aws_vpc" "vpc_peer_to" {
  filter {
    name   = "tag:Name"
    values = ["non-prod-workload"]
  }
}

resource "aws_vpc_peering_connection" "peer_prod_infra_nonprod_workload" {
  peer_owner_id = data.aws_caller_identity.current.account_id
  peer_vpc_id   = data.aws_vpc.vpc_peer_to.id
  vpc_id        = data.aws_vpc.vpc_peer_from.id
  auto_accept   = true

  tags = {
    Name = "VPC Peering between prod infra vpc and non-prod workload vpc"
  }
}

data "aws_route_table" "rt_peer_to" {
  vpc_id = data.aws_vpc.vpc_peer_to.id
  filter {
    name   = "tag:Name"
    values = ["non-prod-workload-private"]
  }
}

data "aws_route_table" "rt_peer_from" {
  vpc_id = data.aws_vpc.vpc_peer_from.id
  filter {
    name   = "tag:Name"
    values = ["prod-infra-private"]
  }
}

resource "aws_route" "peer_to_route" {
  route_table_id            = data.aws_route_table.rt_peer_to.id
  destination_cidr_block    = data.aws_vpc.vpc_peer_from.cidr_block
  vpc_peering_connection_id = aws_vpc_peering_connection.peer_prod_infra_nonprod_workload.id
}

resource "aws_route" "peer_from_route" {
  route_table_id            = data.aws_route_table.rt_peer_from.id
  destination_cidr_block    = data.aws_vpc.vpc_peer_to.cidr_block
  vpc_peering_connection_id = aws_vpc_peering_connection.peer_prod_infra_nonprod_workload.id
}
