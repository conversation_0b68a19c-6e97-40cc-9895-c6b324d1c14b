#!/bin/sh

# $1: plan, apply

# Lint clean
terraform fmt

case "$1" in
    "plan")
        terraform init --backend-config="key=tfstate/peering" -reconfigure
        terraform plan;;
    "apply")
        terraform init --backend-config="key=tfstate/peering" -reconfigure
        terraform apply;;
    "refresh")
        terraform init --backend-config="key=tfstate/peering" -reconfigure
        terraform apply;;
    "destroy")
        terraform destroy;;
    *)
        echo "Usage ./run <plan|apply|destroy>"
        exit 1;;
esac
