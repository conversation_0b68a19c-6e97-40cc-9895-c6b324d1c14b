locals {
  region = var.aws_region
  tags = {
    # Reserved for MAP program
    # "map-migrated" = "d-server-0264tzk2na5vsp"
    profile = var.profile
  }
  k8s_sys_service_account_namespace = "kube-system"

  infra_k8s_version        = "1.28"
  infra_vpc_name           = "${var.profile}-infra"
  infra_ebs_csi_driver_arn = "arn:aws-cn:iam::************:role/non-prod-infra-aws-ebs-csi-driver"

  workload_k8s_version        = "1.28"
  workload_vpc_name           = "${var.profile}-workload"
  workload_ebs_csi_driver_arn = "arn:aws-cn:iam::************:role/non-prod-workload-aws-ebs-csi-driver"
}
