{"Statement": [{"Action": ["acm:DescribeCertificate", "acm:ListCertificates", "cognito-idp:DescribeUserPoolClient", "iam:GetServerCertificate", "iam:ListServerCertificates", "shield:CreateProtection", "shield:DeleteProtection", "shield:DescribeProtection", "shield:GetSubscriptionState", "waf-regional:AssociateWebACL", "waf-regional:DisassociateWebACL", "waf-regional:GetWebACL", "waf-regional:GetWebACLForResource", "wafv2:AssociateWebACL", "wafv2:DisassociateWebACL", "wafv2:GetWebACL", "wafv2:GetWebACLForResource"], "Effect": "Allow", "Resource": "*"}, {"Action": ["ec2:AuthorizeSecurityGroupIngress", "ec2:DeleteSecurityGroup", "ec2:RevokeSecurityGroupIngress"], "Condition": {"Null": {"aws:ResourceTag/elbv2.k8s.aws/cluster": "false"}}, "Effect": "Allow", "Resource": "*"}, {"Action": ["ec2:AuthorizeSecurityGroupIngress", "ec2:RevokeSecurityGroupIngress"], "Effect": "Allow", "Resource": "*"}, {"Action": ["ec2:CreateSecurityGroup"], "Effect": "Allow", "Resource": "*"}, {"Action": ["ec2:CreateTags", "ec2:DeleteTags"], "Condition": {"Null": {"aws:RequestTag/elbv2.k8s.aws/cluster": "true", "aws:ResourceTag/elbv2.k8s.aws/cluster": "false"}}, "Effect": "Allow", "Resource": "arn:aws-cn:ec2:*:*:security-group/*"}, {"Action": ["ec2:CreateTags"], "Condition": {"Null": {"aws:RequestTag/elbv2.k8s.aws/cluster": "false"}, "StringEquals": {"ec2:CreateAction": "CreateSecurityGroup"}}, "Effect": "Allow", "Resource": "arn:aws-cn:ec2:*:*:security-group/*"}, {"Action": ["ec2:DescribeAccountAttributes", "ec2:DescribeAddresses", "ec2:DescribeAvailabilityZones", "ec2:DescribeCoipPools", "ec2:DescribeInstances", "ec2:DescribeInternetGateways", "ec2:DescribeNetworkInterfaces", "ec2:DescribeSecurityGroups", "ec2:DescribeSubnets", "ec2:DescribeTags", "ec2:DescribeVpcPeeringConnections", "ec2:DescribeVpcs", "ec2:GetCoipPoolUsage", "elasticloadbalancing:DescribeListenerCertificates", "elasticloadbalancing:DescribeListeners", "elasticloadbalancing:DescribeLoadBalancerAttributes", "elasticloadbalancing:DescribeLoadBalancers", "elasticloadbalancing:DescribeRules", "elasticloadbalancing:DescribeSSLPolicies", "elasticloadbalancing:DescribeTags", "elasticloadbalancing:DescribeTargetGroupAttributes", "elasticloadbalancing:DescribeTargetGroups", "elasticloadbalancing:DescribeTargetHealth"], "Effect": "Allow", "Resource": "*"}, {"Action": ["elasticloadbalancing:AddListenerCertificates", "elasticloadbalancing:ModifyListener", "elasticloadbalancing:ModifyRule", "elasticloadbalancing:RemoveListenerCertificates", "elasticloadbalancing:SetWebAcl"], "Effect": "Allow", "Resource": "*"}, {"Action": ["elasticloadbalancing:AddTags", "elasticloadbalancing:RemoveTags"], "Condition": {"Null": {"aws:RequestTag/elbv2.k8s.aws/cluster": "true", "aws:ResourceTag/elbv2.k8s.aws/cluster": "false"}}, "Effect": "Allow", "Resource": ["arn:aws-cn:elasticloadbalancing:*:*:loadbalancer/app/*/*", "arn:aws-cn:elasticloadbalancing:*:*:loadbalancer/net/*/*", "arn:aws-cn:elasticloadbalancing:*:*:targetgroup/*/*"]}, {"Action": ["elasticloadbalancing:AddTags", "elasticloadbalancing:RemoveTags"], "Effect": "Allow", "Resource": ["arn:aws-cn:elasticloadbalancing:*:*:listener-rule/app/*/*/*", "arn:aws-cn:elasticloadbalancing:*:*:listener-rule/net/*/*/*", "arn:aws-cn:elasticloadbalancing:*:*:listener/app/*/*/*", "arn:aws-cn:elasticloadbalancing:*:*:listener/net/*/*/*"]}, {"Action": ["elasticloadbalancing:CreateListener", "elasticloadbalancing:CreateRule", "elasticloadbalancing:DeleteListener", "elasticloadbalancing:DeleteRule"], "Effect": "Allow", "Resource": "*"}, {"Action": ["elasticloadbalancing:CreateLoadBalancer", "elasticloadbalancing:CreateTargetGroup"], "Condition": {"Null": {"aws:RequestTag/elbv2.k8s.aws/cluster": "false"}}, "Effect": "Allow", "Resource": "*"}, {"Action": ["elasticloadbalancing:DeleteLoadBalancer", "elasticloadbalancing:DeleteTargetGroup", "elasticloadbalancing:ModifyLoadBalancerAttributes", "elasticloadbalancing:ModifyTargetGroup", "elasticloadbalancing:ModifyTargetGroupAttributes", "elasticloadbalancing:SetIpAddressType", "elasticloadbalancing:SetSecurityGroups", "elasticloadbalancing:SetSubnets"], "Condition": {"Null": {"aws:ResourceTag/elbv2.k8s.aws/cluster": "false"}}, "Effect": "Allow", "Resource": "*"}, {"Action": ["elasticloadbalancing:DeregisterTargets", "elasticloadbalancing:RegisterTargets"], "Effect": "Allow", "Resource": "arn:aws-cn:elasticloadbalancing:*:*:targetgroup/*/*"}, {"Action": ["iam:CreateServiceLinkedRole"], "Condition": {"StringEquals": {"iam:AWSServiceName": "elasticloadbalancing.amazonaws.com"}}, "Effect": "Allow", "Resource": "*"}], "Version": "2012-10-17"}