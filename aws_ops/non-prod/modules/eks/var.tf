variable "profile" {
  type    = string
  default = "non-prod"
}

variable "cluster_name" {
  type = string
}

variable "cluster_tags" {
}

variable "crossplane_create_role" {
  type = bool
}

variable "k8s_version" {
  type = string
}

variable "k8s_sys_service_account_namespace" {
  type = string
}

variable "vpc_name" {
  type = string
}

variable "eks_node_type" {
  type = string
}

variable "elastic_node_type" {
  type = list(string)
}

variable "ebs_csi_driver_arn" {
  type = string
}
