#!/bin/sh

# $1: plan, apply
# $2: nonprod, prod

# Lint clean
terraform fmt

case "$1" in
    "plan")
        terraform init --backend-config="key=tfstate/infra-$2" -reconfigure
        terraform plan --var-file input_$2.tfvars;;
    "apply")
        terraform init --backend-config="key=tfstate/infra-$2" -reconfigure
        terraform apply --var-file input_$2.tfvars;;
    "destroy")
        terraform destroy --var-file input_$2.tfvars;;
    *)
        echo "Usage ./run <plan|apply|destroy> <nonprod|prod>"
        exit 1;;
esac