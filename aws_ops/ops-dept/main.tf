#
# ops department vpc
# public only, isolated to all other vpcs
#
data "aws_availability_zones" "available" {}

module "ops_dept_vpc" {
  source  = "terraform-aws-modules/vpc/aws"
  version = "3.13.0"

  name = var.ops_vpc_name
  cidr = var.ops_vpc_cidr

  azs = data.aws_availability_zones.available.names

  propagate_private_route_tables_vgw = true
  private_subnets                    = var.ops_pri_subnets
  private_subnet_tags = {
    subnet_type = "private"
  }

  public_subnets = var.ops_pub_subnets
  public_subnet_tags = {
    subnet_type = "public"
  }

  manage_default_route_table = true
  default_route_table_tags   = { DefaultRouteTable = true }

  enable_dns_hostnames = true
  enable_dns_support   = true

  # don't enable nat gateway unless it is necessary
  enable_nat_gateway = false
  # single_nat_gateway = true

  # Default security group - ingress/egress rules cleared to deny all
  manage_default_security_group = true
  # doesn't open any inbound traffic
  default_security_group_ingress = []
  # default allow all outbound traffic
  default_security_group_egress = [
    {
      from_port   = 0
      to_port     = 0
      protocol    = "-1"
      cidr_blocks = "0.0.0.0/0"
    }
  ]

  enable_flow_log = false

  tags = {
    profile = "ops"
    owner   = "konvery-sre"
  }
}

resource "aws_security_group" "ops_dept_sg" {
  description = "rules for ops server"
  name        = "ops_ec2_security_group"
  vpc_id      = module.ops_dept_vpc.vpc_id

  ingress {
    description = "rdp"
    from_port   = 3389
    to_port     = 3389
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    description = "ssh"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["*************/32", "*************/32"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = -1
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "aws-ops-sg"
  }
}

# module "ops_dept_ec2_module" {
#   source = "./modules/ec2"

#   availability_zone  = module.ops_dept_vpc.azs[0]
#   subnet_id          = module.ops_dept_vpc.public_subnets[0]
#   security_group_ids = [aws_security_group.ops_dept_sg.id]

#   ops_ins_type = "c6i.large"
#   ops_ebs_size = "100"
# }

# output "ops_dept_ec2_module_outputs" {
#   value = module.ops_dept_ec2_module
# }
