
# data "aws_ami" "ops_dept_ami" {
#   most_recent = true

#   filter {
#     name   = "virtualization-type"
#     values = ["hvm"]
#   }

#   filter {
#     name   = "name"
#     values = ["Windows_Server-2022-Chinese_Simplified-Full-Base-*"]
#   }

#   owners = ["amazon", "837727238323"]
# }

# customized ami
data "aws_ami" "ops_dept_ami" {
  filter {
    name   = "image-id"
    values = ["ami-0e9ec0af8cd5ecce1"]
  }
}

data "aws_key_pair" "ops_dept_keypair" {
  filter {
    name   = "key-name"
    values = ["konvery-sre-key"]
  }
}

# Launch ec2
resource "aws_instance" "ops_dept_ec2" {
  ami               = data.aws_ami.ops_dept_ami.id
  availability_zone = var.availability_zone
  instance_type     = var.ops_ins_type
  key_name          = data.aws_key_pair.ops_dept_keypair.key_name
  subnet_id         = var.subnet_id

  root_block_device {
    volume_type = "gp3"
    volume_size = var.ops_ebs_size
  }

  vpc_security_group_ids = var.security_group_ids
  # get_password_data      = "true"

  # provisioner "remote-exec" {
  #   inline = ["echo hello world"]
  #   connection {
  #     type     = "winrm"
  #     password = rsadecrypt(aws_instance.ec2.password_data, tls_private_key.key.private_key_pem)
  #   }
  # }

  tags = {
    Name = "aws-ops-ec2"
  }
}

resource "aws_eip" "ops_dept_eip" {
  instance = aws_instance.ops_dept_ec2.id
  vpc      = true
  tags = {
    Name = "aws-ops-eip"
  }
}

output "instance_id" {
  value = aws_instance.ops_dept_ec2.id
}

output "instance_ip" {
  value = aws_eip.ops_dept_eip.public_ip
}

# This password is used when creating ami with standard aws ami

# output "connection_password" {
#   value = rsadecrypt(aws_instance.ops_dept_ec2.password_data, file("/Users/<USER>/Konvery/RD/Infra/konvery-sre-key.pem"))
# }
