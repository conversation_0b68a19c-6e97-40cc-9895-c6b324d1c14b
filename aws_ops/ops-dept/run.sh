#!/bin/sh

# $1: plan, apply

# Lint clean
terraform fmt

case "$1" in
    "plan")
        terraform init --backend-config="key=tfstate/ops" -reconfigure
        terraform plan;;
    "apply")
        terraform init --backend-config="key=tfstate/ops" -reconfigure
        terraform apply;;
    "refresh")
        terraform init --backend-config="key=tfstate/ops" -reconfigure
        terraform apply --refresh-only;;
    "destroy")
        terraform destroy;;
    *)
        echo "Usage ./run <plan|apply|destroy>"
        exit 1;;
esac
