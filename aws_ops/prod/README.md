# ReadMe

This part of operations mainly launch and configure following components:

* eks with aws manged node
* karpenter for autoscaling
* aws network load balancer for application routing
* crossplane role

Launching kubernetes servers on AWS is relatively simple. Just follow the steps below to get EKS working.

awscli is required to run this terraform service

## Provision & de-provision infrastructure

### Apply any change

```shell
# Make sure you have correct dns to resolve the s3 url
# compare the result vs www.whatsmydns.net
dig xxx
./run.sh <plan|apply> <prod>
```

### Remove everything in this folder

```shell
./run.sh destroy <nonprod|prod>
# make sure you clearly check the change before hitting "yes"
```

### Remove selected items

```shell
# remove relevant files from the folder, and ./run.sh apply <env>
```

You will then get a workable running environment

## EKS notes

...

## Karpenter notes

### Linked role

``` json
{
    "Role": {
        "Path": "/aws-service-role/spot.amazonaws.com/",
        "RoleName": "AWSServiceRoleForEC2Spot",
        "RoleId": "AROAQQRPGTTKXEBS7UEDL",
        "Arn": "arn:aws-cn:iam::************:role/aws-service-role/spot.amazonaws.com/AWSServiceRoleForEC2Spot",
        "CreateDate": "2022-04-19T07:19:53+00:00",
        "AssumeRolePolicyDocument": {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Action": [
                        "sts:AssumeRole"
                    ],
                    "Effect": "Allow",
                    "Principal": {
                        "Service": [
                            "spot.amazonaws.com"
                        ]
                    }
                }
            ]
        }
    }
}
```

## AWS network load balancer notes

...
