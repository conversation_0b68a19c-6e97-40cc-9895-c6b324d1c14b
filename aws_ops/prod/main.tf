data "aws_caller_identity" "current" {}
data "aws_availability_zones" "available" {}

module "infra_cluster" {
  source = "./modules/eks"

  cluster_name                      = "infra"
  cluster_tags                      = local.tags
  crossplane_create_role            = true
  k8s_sys_service_account_namespace = local.k8s_sys_service_account_namespace
  k8s_version                       = local.infra_k8s_version
  profile                           = var.profile
  vpc_name                          = local.infra_vpc_name
  eks_node_type                     = var.infra_node
  elastic_node_type                 = var.infra_elastic_node
  ebs_csi_driver_arn                = local.infra_ebs_csi_driver_arn
}

module "workload_cluster" {
  source = "./modules/eks"

  cluster_name                      = "workload"
  cluster_tags                      = local.tags
  crossplane_create_role            = true
  k8s_sys_service_account_namespace = local.k8s_sys_service_account_namespace
  k8s_version                       = local.workload_k8s_version
  profile                           = var.profile
  vpc_name                          = local.workload_vpc_name
  eks_node_type                     = var.workload_node
  elastic_node_type                 = var.workload_elastic_node
  ebs_csi_driver_arn                = local.workload_ebs_csi_driver_arn
}
