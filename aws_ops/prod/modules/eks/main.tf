locals {
  cluster_name = "${var.profile}-${var.cluster_name}-cluster"

  elb_service_account_name        = "${var.profile}-${var.cluster_name}-aws-load-balancer-controller"
  crossplane_service_account_name = "${var.profile}-${var.cluster_name}-crossplane-provider"

  # Used to determine correct partition (i.e. - `aws`, `aws-gov`, `aws-cn`, etc.)
  partition = data.aws_partition.current.partition
}

data "aws_caller_identity" "current" {}
data "aws_partition" "current" {}

data "aws_eks_cluster_auth" "eks" {
  name = module.eks.cluster_id
}

data "aws_vpc" "legacy_vpc" {
  filter {
    name   = "tag:profile"
    values = ["legacy"]
  }
}

data "aws_vpc" "vpc" {
  tags = {
    "Name" = var.vpc_name
  }
}

data "aws_subnets" "private_subnets" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.vpc.id]
  }

  tags = {
    "subnet_type" = "private"
  }
}

data "aws_key_pair" "access_key_pair" {
  filter {
    name   = "key-name"
    values = ["konvery-sre-key"]
  }
}

resource "aws_security_group" "legacy_tls" {
  name        = "allow_legacy_tls"
  description = "Allow eks endpoint inbound tls traffic from legacy vpc"
  vpc_id      = data.aws_vpc.vpc.id

  ingress {
    description = "TLS from legacy VPC"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = [data.aws_vpc.legacy_vpc.cidr_block]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  tags = {
    Name = "allow_legacy_tls"
  }
}

# an eks cluster that takes on-demand & spot instances
module "eks" {
  # https://registry.terraform.io/modules/terraform-aws-modules/eks/aws/latest
  source  = "terraform-aws-modules/eks/aws"
  version = "~>18.30"

  cluster_endpoint_private_access = true
  cluster_endpoint_public_access  = false
  cluster_name                    = local.cluster_name
  cluster_tags                    = var.cluster_tags
  cluster_version                 = var.k8s_version
  enable_irsa                     = true
  subnet_ids                      = data.aws_subnets.private_subnets.ids
  vpc_id                          = data.aws_vpc.vpc.id

  # cluster_enabled_log_types = ["audit", "api", "authenticator"]
  cluster_enabled_log_types = []

  # Extend cluster security group rules
  cluster_additional_security_group_ids = [aws_security_group.legacy_tls.id]

  # https://github.com/terraform-aws-modules/terraform-aws-eks/issues/1904
  cluster_iam_role_dns_suffix = "amazonaws.com"

  #
  # Karpenter
  #
  # We will rely only on the cluster security group created by the EKS service
  # See note below for `tags`
  create_cluster_security_group = false
  create_node_security_group    = false

  # Only need one node to get Karpenter up and running.
  # This ensures core services such as VPC CNI, CoreDNS, etc. are up and running
  # so that Karpetner can be deployed and start managing compute capacity as required
  eks_managed_node_groups = {
    karpenter = {
      instance_types = [var.eks_node_type]
      # We don't need the node security group since we are using the
      # cluster-created security group, which Karpenter will also use
      create_security_group                 = false
      attach_cluster_primary_security_group = true

      min_size     = 3
      max_size     = 3
      desired_size = 3

      block_device_mappings = {
        xvda = {
          device_name = "/dev/xvda"
          ebs = {
            volume_size = 64
            volume_type = "gp3"
          }
        }
      }

      labels = {
        partition = "workload"
      }

      key_name = data.aws_key_pair.access_key_pair.key_name

      iam_role_additional_policies = [
        # Required by Karpenter
        # Hardcoded to aws-cn
        "arn:${local.partition}:iam::aws:policy/AmazonSSMManagedInstanceCore"
      ]
    }
  }

  tags = merge(var.cluster_tags, {
    # Tag node group resources for Karpenter auto-discovery
    # NOTE - if creating multiple security groups with this module, only tag the
    # security group that Karpenter should utilize with the following tag
    "karpenter.sh/discovery" = local.cluster_name
  })
}

resource "aws_eks_addon" "aws-ebs-csi-driver" {
  cluster_name = module.eks.cluster_id

  addon_name               = "aws-ebs-csi-driver"
  addon_version            = "v1.25.0-eksbuild.1"
  service_account_role_arn = var.ebs_csi_driver_arn

  tags = var.cluster_tags
}

resource "aws_eks_addon" "coredns" {
  cluster_name = module.eks.cluster_id

  addon_name    = "coredns"
  addon_version = "v1.10.1-eksbuild.6"

  tags = var.cluster_tags
}

resource "aws_eks_addon" "kube-proxy" {
  cluster_name = module.eks.cluster_id

  addon_name    = "kube-proxy"
  addon_version = "v1.28.2-eksbuild.2"

  tags = var.cluster_tags
}

resource "aws_eks_addon" "vpc-cni" {
  cluster_name = module.eks.cluster_id

  addon_name    = "vpc-cni"
  addon_version = "v1.15.4-eksbuild.1"
  configuration_values = jsonencode({
    enableNetworkPolicy = "true"
  })

  tags = var.cluster_tags
}

provider "helm" {
  kubernetes {
    host                   = module.eks.cluster_endpoint
    cluster_ca_certificate = base64decode(module.eks.cluster_certificate_authority_data)

    exec {
      api_version = "client.authentication.k8s.io/v1beta1"
      command     = "aws"
      args        = ["eks", "get-token", "--cluster-name", local.cluster_name]
    }
  }
}

resource "aws_iam_instance_profile" "karpenter" {
  name = "KarpenterNodeInstanceProfile-${local.cluster_name}"
  role = module.eks.eks_managed_node_groups["karpenter"].iam_role_name
}

module "karpenter_irsa" {
  source  = "terraform-aws-modules/iam/aws//modules/iam-role-for-service-accounts-eks"
  version = "~> 5.32"

  role_name = "karpenter-controller-${local.cluster_name}"

  attach_karpenter_controller_policy = true

  karpenter_controller_cluster_id = module.eks.cluster_id
  karpenter_controller_ssm_parameter_arns = [
    "arn:${local.partition}:ssm:*:*:parameter/aws/service/*"
  ]
  karpenter_controller_node_iam_role_arns = [
    module.eks.eks_managed_node_groups["karpenter"].iam_role_arn
  ]

  oidc_providers = {
    ex = {
      provider_arn               = module.eks.oidc_provider_arn
      namespace_service_accounts = ["karpenter:karpenter"]
    }
  }
}

resource "helm_release" "karpenter-crd" {
  namespace        = "karpenter"
  create_namespace = false

  name    = "karpenter-crd"
  chart   = "oci://public.ecr.aws/karpenter/karpenter-crd"
  version = "v0.32.2"
}

resource "helm_release" "karpenter" {
  namespace        = "karpenter"
  create_namespace = false

  name    = "karpenter"
  chart   = "oci://public.ecr.aws/karpenter/karpenter"
  version = "v0.32.2"

  set {
    name  = "serviceAccount.annotations.eks\\.amazonaws\\.com/role-arn"
    value = module.karpenter_irsa.iam_role_arn
  }

  set {
    name  = "settings.aws.clusterName"
    value = module.eks.cluster_id
  }

  set {
    name  = "settings.aws.clusterEndpoint"
    value = module.eks.cluster_endpoint
  }

  set {
    name  = "settings.aws.defaultInstanceProfile"
    value = aws_iam_instance_profile.karpenter.name
  }

  set {
    name  = "controller.resources.requests.cpu"
    value = "100m"
  }

  set {
    name  = "controller.resources.requests.memory"
    value = "200M"
  }

  depends_on = [
    helm_release.karpenter-crd
  ]
}

provider "kubectl" {
  apply_retry_count      = 5
  host                   = module.eks.cluster_endpoint
  cluster_ca_certificate = base64decode(module.eks.cluster_certificate_authority_data)
  load_config_file       = false

  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    command     = "aws"
    args        = ["eks", "get-token", "--cluster-name", module.eks.cluster_id]
  }
}

resource "kubectl_manifest" "karpenter_nodeclass" {
  yaml_body = <<-YAML
  apiVersion: karpenter.k8s.aws/v1beta1
  kind: EC2NodeClass
  metadata:
    name: default
  spec:
    amiFamily: AL2
    instanceProfile: "KarpenterNodeInstanceProfile-${local.cluster_name}"
    securityGroupSelectorTerms:
      - tags:
          karpenter.sh/discovery: ${local.cluster_name}
    subnetSelectorTerms:
      - tags:
          karpenter.sh/discovery: ${local.cluster_name}
    tags:
      karpenter.sh/discovery: ${local.cluster_name}
    blockDeviceMappings:
      - deviceName: /dev/xvda
        ebs:
          volumeSize: 48Gi
          volumeType: gp3
          deleteOnTermination: true
  YAML

  depends_on = [
    helm_release.karpenter
  ]
}

# Workaround - https://github.com/hashicorp/terraform-provider-kubernetes/issues/1380#issuecomment-967022975
resource "kubectl_manifest" "karpenter_nodepool" {
  yaml_body = <<-YAML
  apiVersion: karpenter.sh/v1beta1
  kind: NodePool
  metadata:
    name: default
  spec:
    limits:
      cpu: "128"
    template:
      metadata:
        labels:
          partition: workload
      spec:
        nodeClassRef:
          name: default
        requirements:
          - key: karpenter.sh/capacity-type
            operator: In
            values: ["spot"]
          - key: node.kubernetes.io/instance-type
            operator: In
            values: ${jsonencode(var.elastic_node_type)}
  YAML

  depends_on = [
    helm_release.karpenter,
    kubectl_manifest.karpenter_nodeclass
  ]
}

resource "kubectl_manifest" "karpenter_monitoring_nodepool" {
  yaml_body = <<-YAML
  apiVersion: karpenter.sh/v1beta1
  kind: NodePool
  metadata:
    name: monitoring
  spec:
    limits:
      cpu: "16"
    template:
      metadata:
        labels:
          partition: monitoring
      spec:
        nodeClassRef:
          name: default
        requirements:
          - key: karpenter.sh/capacity-type
            operator: In
            values: ["spot"]
          - key: node.kubernetes.io/instance-type
            operator: In
            values: ${jsonencode(var.elastic_node_type)}
        taints:
          - key: monitoring
            effect: NoSchedule
  YAML

  depends_on = [
    helm_release.karpenter,
    kubectl_manifest.karpenter_nodeclass
  ]
}

resource "aws_iam_policy" "elb_controller_policy" {
  name        = local.elb_service_account_name
  description = "Worker policy for the ELB"

  # https://github.com/kubernetes-sigs/aws-load-balancer-controller/blob/main/docs/install/iam_policy_cn.json
  policy = file("${path.module}/conf/elb-policy.json")
}

module "iam_assumable_role_elb" {
  source  = "terraform-aws-modules/iam/aws//modules/iam-assumable-role-with-oidc"
  version = "~> 5.1" # need to be var

  create_role                   = true
  oidc_fully_qualified_subjects = ["system:serviceaccount:${var.k8s_sys_service_account_namespace}:${local.elb_service_account_name}"]
  provider_url                  = replace(module.eks.cluster_oidc_issuer_url, "https://", "")
  role_name                     = local.elb_service_account_name
  role_policy_arns              = [aws_iam_policy.elb_controller_policy.arn]
}

resource "helm_release" "aws_load_balancer_controller" {
  name       = "aws-load-balancer-controller"
  chart      = "aws-load-balancer-controller"
  repository = "https://aws.github.io/eks-charts"
  # version    = "~> 1.6.0"
  namespace = var.k8s_sys_service_account_namespace

  set {
    name  = "clusterName"
    value = local.cluster_name
  }

  set {
    name  = "serviceAccount.annotations.eks\\.amazonaws\\.com/role-arn"
    value = "arn:aws-cn:iam::${data.aws_caller_identity.current.account_id}:role/${local.elb_service_account_name}"
  }

  set {
    name  = "serviceAccount.name"
    value = local.elb_service_account_name
  }

  depends_on = [
    module.eks
  ]
}

module "iam_assumable_role_crossplane" {
  source  = "terraform-aws-modules/iam/aws//modules/iam-assumable-role-with-oidc"
  version = "~> 5.1" # need to be var

  create_role                  = var.crossplane_create_role
  oidc_subjects_with_wildcards = ["system:serviceaccount:crossplane-system:${local.crossplane_service_account_name}-*"]
  provider_url                 = replace(module.eks.cluster_oidc_issuer_url, "https://", "")
  role_name                    = local.crossplane_service_account_name

  # Administrator
  role_policy_arns = ["arn:${local.partition}:iam::aws:policy/AdministratorAccess"]
}
