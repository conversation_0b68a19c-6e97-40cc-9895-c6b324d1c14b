terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~>5.16"
    }

    helm = {
      source  = "hashicorp/helm"
      version = "~>2.7"
    }

    kubectl = {
      source  = "gavin<PERSON><PERSON>/kubectl"
      version = "~>1.14"
    }
  }

  backend "s3" {
    bucket = "konvery-terraform"
    region = "cn-northwest-1"
  }
}

provider "aws" {
  region = "cn-northwest-1"
}
