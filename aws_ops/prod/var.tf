variable "profile" {
  type    = string
  default = ""
}

variable "infra_node" {
  type = string
}

variable "workload_node" {
  type = string
}

variable "infra_elastic_node" {
  type = list(string)
}

variable "workload_elastic_node" {
  type = list(string)
}

variable "aws_region" {
  type    = string
  default = "cn-northwest-1"
}

variable "tag_owner" {
  type    = string
  default = "konvery-sre"
}
