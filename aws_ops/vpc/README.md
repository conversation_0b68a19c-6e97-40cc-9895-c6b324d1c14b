# ReadMe

This part of operations mainly launch and configure following components:

* vpc
* network
* peering
* routetable

## Provision & de-provision infrastructure

### Apply any change

```shell
# Make sure you have correct dns to resolve the s3 url
# compare the result vs www.whatsmydns.net
dig xxx
./run.sh <plan|apply> <nonprod|prod>
```

### Remove everything in this folder

```shell
./run.sh destroy <nonprod|prod>
# make sure you clearly check the change before hitting "yes"
```

### Remove selected items

```shell
# remove relevant files from the folder, and ./run.sh apply <env>
```

You will then get a workable running environment

### Todo
