# Production Environment
profile = "prod"

# VPC for infra nonprod
infra_main_cidr   = "*********/16"
infra_pri_subnets = ["*********/18", "**********/18", "***********/18"]
infra_pub_subnets = ["***********/20", "***********/20", "***********/20"]
infra_db_subnets  = ["***********/22", "***********/22", "***********/22"]
# infra_lambda_subnets = []

# infra_trusted_external_list = {
#   # in = {
#   #   type      = "ingress"
#   #   from_port = 22
#   #   to_port   = 22
#   #   protocol  = "tcp"
#   #   cidr      = ["**************/32"]
#   #   des       = "home IP"
#   # }
#   out = {
#     type      = "egress"
#     from_port = 0
#     to_port   = 65535
#     protocol  = "all"
#     cidr      = ["0.0.0.0/0"]
#     des       = "open egress to outside"
#   }
# }

# Additional route for infra public subnet
# Reserver for vpn instance?
# infra_pub_subnets_route_supplement = "************/20"

# VPC for workload production
workload_main_cidr   = "*********/16"
workload_pri_subnets = ["*********/18", "**********/18", "***********/18"]
workload_pub_subnets = ["***********/20", "***********/20", "***********/20"]
workload_db_subnets  = ["***********/22", "***********/22", "***********/22"]
