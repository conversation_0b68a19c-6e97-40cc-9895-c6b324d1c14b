locals {
  region = var.aws_region

  tags = {
    owner   = var.tag_owner
    profile = var.profile
    # reserved for MAP program
    #  "map-migrated" = "d-server-0264tzk2na5vsp"
  }

  infra_vpc_name        = "${var.profile}-infra"
  workload_vpc_name     = "${var.profile}-workload"
  infra_cluster_name    = "${var.profile}-infra-cluster"
  workload_cluster_name = "${var.profile}-workload-cluster"

  # peering between private subnets of different vpcs
  infra_peer_workload_routes = flatten([
    for subnet in var.workload_pri_subnets : [
      for rtb_id in module.infra_vpc.private_route_table_ids : {
        route_table_id            = rtb_id
        destination_cidr_block    = subnet
        vpc_peering_connection_id = aws_vpc_peering_connection.infra_peer_workload.id
      }
    ]
  ])

  # peering between private subnets of different vpcs
  workload_peer_infra_routes = flatten([
    for subnet in var.infra_pri_subnets : [
      for rtb_id in module.workload_vpc.private_route_table_ids : {
        route_table_id            = rtb_id
        destination_cidr_block    = subnet
        vpc_peering_connection_id = aws_vpc_peering_connection.infra_peer_workload.id
      }
    ]
  ])
}
