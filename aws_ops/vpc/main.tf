provider "aws" {
  region = "cn-northwest-1"
}

data "aws_availability_zones" "available" {}
data "aws_caller_identity" "current" {}

# Reserved for dx connection in the future
# data "aws_dx_gateway" "konvery_dx_gw" {
#   name = "KONVERY_PEK_AWS_NINGXIA"
# }

################################################################################
# VPC Module
################################################################################

#
# infra vpc
#
module "infra_vpc" {
  source  = "terraform-aws-modules/vpc/aws"
  version = "~> 3.0"

  name = local.infra_vpc_name
  cidr = var.infra_main_cidr

  azs = data.aws_availability_zones.available.names

  # --- private subnet
  # https://aws.amazon.com/cn/premiumsupport/knowledge-center/eks-vpc-subnet-discovery/
  propagate_private_route_tables_vgw = true
  private_subnets                    = var.infra_pri_subnets
  private_subnet_tags = {
    subnet_type = "private"

    "kubernetes.io/cluster/${local.infra_cluster_name}" = "owned"
    # "kubernetes.io/cluster/${local.infra_cluster_name}" = "shared"
    "kubernetes.io/role/internal-elb" = "1"

    # Tags subnets for Karpenter auto-discovery
    "karpenter.sh/discovery" = local.infra_cluster_name
  }

  # --- public subnet
  public_subnets = var.infra_pub_subnets
  public_subnet_tags = {
    subnet_type = "public"

    "kubernetes.io/cluster/${local.infra_cluster_name}" = "shared"
    "kubernetes.io/role/elb"                            = "1"
  }

  # --- database subnet
  database_subnets = var.infra_db_subnets
  database_subnet_tags = {
    subnet_type = "database"
  }
  create_database_subnet_group = true

  # vgw configuration
  #   enable_vpn_gateway = true
  #   vpn_gateway_tags = {
  #     source = "BX-ALIBJ-ECV-SG"
  #   }

  manage_default_route_table = true
  default_route_table_tags   = { DefaultRouteTable = true }
  database_route_table_tags  = var.tag_peer_to_legacy
  private_route_table_tags   = var.tag_peer_to_legacy
  public_route_table_tags    = var.tag_peer_to_legacy

  # For EKS endpoint access
  enable_dns_hostnames = true
  enable_dns_support   = true

  # nat gateway configuration;
  # do we need multiple nat gateway in prod?
  enable_nat_gateway = true
  single_nat_gateway = true

  # Default security group - ingress/egress rules cleared to deny all
  manage_default_security_group = true
  # doesn't open any inbound traffic
  default_security_group_ingress = []
  # default allow all outbound traffic
  default_security_group_egress = [
    {
      from_port   = 0
      to_port     = 0
      protocol    = "-1"
      cidr_blocks = "0.0.0.0/0"
    }
  ]

  # VPC Flow Logs (Cloudwatch log group and IAM role will be created)
  enable_flow_log                                 = true
  create_flow_log_cloudwatch_log_group            = true
  create_flow_log_cloudwatch_iam_role             = true
  flow_log_max_aggregation_interval               = 600
  flow_log_cloudwatch_log_group_retention_in_days = var.infra_flow_log_retention

  tags = local.tags
}

data "aws_security_group" "infra_default_sg" {
  name   = "default"
  vpc_id = module.infra_vpc.vpc_id
}

module "infra_vpc_endpoint" {
  source  = "terraform-aws-modules/vpc/aws//modules/vpc-endpoints"
  version = "~> 3.0"

  vpc_id             = module.infra_vpc.vpc_id
  security_group_ids = [data.aws_security_group.infra_default_sg.id]

  endpoints = {
    s3 = {
      service         = "s3"
      service_type    = "Gateway"
      route_table_ids = flatten([module.infra_vpc.private_route_table_ids, module.infra_vpc.public_route_table_ids])
      tags            = { Name = "s3-infra-vpc-endpoint" }
    },
  }

  tags = merge(local.tags, {
    service_type = "Gateway"
    Endpoint     = "true"
  })
}

# reserved for future dx connection support

# data "aws_vpn_gateway" "infra_vgw" {
#   filter {
#     name   = "tag:Name"
#     values = [local.infra_vpc_name]
#   }
#   attached_vpc_id = module.infra_vpc.vpc_id
# }
#
# resource "aws_dx_gateway_association" "infra_vgw" {
#   dx_gateway_id         = data.aws_dx_gateway.bixin_dx_gw.id
#   associated_gateway_id = data.aws_vpn_gateway.infra_vgw.id

#   allowed_prefixes = [var.infra_main_cidr]
# }

# TODO: Moving haproxy servers to legacy vpc instead of infra vpc
# data "aws_route_table" "infra_pub_route_table" {
#   filter {
#     name  = "tag:Name"
#     values = [local.infra_pub_subnets]
#   }
# }

# resource "aws_route" "infra_pub_subnet_route_supplement" {
#   route_table_id = data.aws_route_table.infra_pub_route_table.id
#   destination_cidr_block = var.infra_pub_subnets_route_supplement
#   gateway_id = data.aws_vpn_gateway.infra_vgw.id
# }

#
# workload vpc
#
module "workload_vpc" {
  source  = "terraform-aws-modules/vpc/aws"
  version = "~> 3.0"

  name = local.workload_vpc_name
  cidr = var.workload_main_cidr

  azs = data.aws_availability_zones.available.names

  # --- private subnet
  # https://aws.amazon.com/cn/premiumsupport/knowledge-center/eks-vpc-subnet-discovery/
  propagate_private_route_tables_vgw = true
  private_subnets                    = var.workload_pri_subnets
  private_subnet_tags = {
    subnet_type = "private"

    "kubernetes.io/cluster/${local.workload_cluster_name}" = "owned"
    # "kubernetes.io/cluster/${local.workload_cluster_name}" = "shared"
    "kubernetes.io/role/internal-elb" = "1"

    # Tags subnets for Karpenter auto-discovery
    "karpenter.sh/discovery" = local.workload_cluster_name
  }

  # --- public subnet
  public_subnets = var.workload_pub_subnets
  public_subnet_tags = {
    subnet_type = "public"

    "kubernetes.io/cluster/${local.workload_cluster_name}" = "shared"
    "kubernetes.io/role/elb"                               = "1"
  }

  # --- database subnet
  database_subnets = var.workload_db_subnets
  database_subnet_tags = {
    subnet_type = "database"
  }
  create_database_subnet_group = true

  # vgw configuration
  #   enable_vpn_gateway = true
  #   vpn_gateway_tags = {
  #     source = "BX-ALIBJ-ECV-SG"
  #   }

  manage_default_route_table = true
  default_route_table_tags   = { DefaultRouteTable = true }
  database_route_table_tags  = var.tag_peer_to_legacy
  private_route_table_tags   = var.tag_peer_to_legacy
  public_route_table_tags    = var.tag_peer_to_legacy

  enable_dns_hostnames = true
  enable_dns_support   = true

  # nat gateway configuration
  # do we need multiple nat gateway in prod?
  enable_nat_gateway = true
  single_nat_gateway = true

  # Default security group - ingress/egress rules cleared to deny all
  manage_default_security_group = true
  # doesn't open any inbound traffic
  default_security_group_ingress = []
  # default allow all outbound traffic
  default_security_group_egress = [
    {
      from_port   = 0
      to_port     = 0
      protocol    = "-1"
      cidr_blocks = "0.0.0.0/0"
    }
  ]

  # VPC Flow Logs (Cloudwatch log group and IAM role will be created)
  enable_flow_log                                 = true
  create_flow_log_cloudwatch_log_group            = true
  create_flow_log_cloudwatch_iam_role             = true
  flow_log_max_aggregation_interval               = 600
  flow_log_cloudwatch_log_group_retention_in_days = var.workload_flow_log_retention

  tags = local.tags
}

data "aws_security_group" "workload_default_sg" {
  name   = "default"
  vpc_id = module.workload_vpc.vpc_id
}

module "workload_vpc_endpoint" {
  source  = "terraform-aws-modules/vpc/aws//modules/vpc-endpoints"
  version = "~> 3.0"

  vpc_id             = module.workload_vpc.vpc_id
  security_group_ids = [data.aws_security_group.workload_default_sg.id]

  endpoints = {
    s3 = {
      service         = "s3"
      service_type    = "Gateway"
      route_table_ids = flatten([module.workload_vpc.private_route_table_ids, module.workload_vpc.public_route_table_ids])
      tags            = { Name = "s3-workload-vpc-endpoint" }
    },
  }

  tags = merge(local.tags, {
    service_type = "Gateway"
    Endpoint     = "true"
  })
}

# reserved for future dx connection support

# data "aws_vpn_gateway" "workload_vgw" {
#   filter {
#     name   = "tag:Name"
#     values = [local.workload_vpc_name]
#   }
#   attached_vpc_id = module.workload_vpc.vpc_id
# }
#
# resource "aws_dx_gateway_association" "workload_vgw" {
#   dx_gateway_id         = data.aws_dx_gateway.bixin_dx_gw.id
#   associated_gateway_id = data.aws_vpn_gateway.workload_vgw.id
#
#   allowed_prefixes = [var.workload_main_cidr]
# }
