resource "aws_vpc_peering_connection" "infra_peer_workload" {
  peer_owner_id = data.aws_caller_identity.current.account_id
  peer_vpc_id   = module.workload_vpc.vpc_id
  vpc_id        = module.infra_vpc.vpc_id
  auto_accept   = true

  tags = {
    Name = "VPC Peering between infra vpc and workload vpc"
  }
}

resource "aws_route" "infra_peer_workload_route" {
  count                     = length(local.infra_peer_workload_routes)
  route_table_id            = local.infra_peer_workload_routes[count.index].route_table_id
  destination_cidr_block    = local.infra_peer_workload_routes[count.index].destination_cidr_block
  vpc_peering_connection_id = local.infra_peer_workload_routes[count.index].vpc_peering_connection_id
}

resource "aws_route" "workload_peer_infra_routes" {
  count                     = length(local.workload_peer_infra_routes)
  route_table_id            = local.workload_peer_infra_routes[count.index].route_table_id
  destination_cidr_block    = local.workload_peer_infra_routes[count.index].destination_cidr_block
  vpc_peering_connection_id = local.workload_peer_infra_routes[count.index].vpc_peering_connection_id
}
