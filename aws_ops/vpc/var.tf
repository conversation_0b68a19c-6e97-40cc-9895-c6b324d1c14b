variable "profile" {
  type    = string
  default = ""
}

variable "aws_region" {
  type    = string
  default = "cn-northwest-1"
}

variable "tag_owner" {
  type    = string
  default = "konvery-sre"
}

variable "tag_peer_to_legacy" {
  type = map(string)
  default = {
    peerlegacy = "true"
  }
}

variable "infra_main_cidr" {
  type = string
}

variable "infra_pri_subnets" {
  type = list(string)
}

variable "infra_pub_subnets" {
  type = list(string)
}

variable "infra_db_subnets" {
  type = list(string)
}

variable "infra_flow_log_retention" {
  type    = number
  default = 7
}

# variable "infra_pub_subnets_route_supplement" {
#   type = string
# }

variable "workload_main_cidr" {
  type = string
}

variable "workload_pri_subnets" {
  type = list(string)
}

variable "workload_pub_subnets" {
  type = list(string)
}

variable "workload_db_subnets" {
  type = list(string)
}

variable "workload_flow_log_retention" {
  type    = number
  default = 7
}

# variable "infra_trusted_external_list" {
#   type = map(object({
#     type      = string
#     from_port = number
#     to_port   = number
#     protocol  = string
#     cidr      = list(string)
#     des       = string
#   }))
# }
