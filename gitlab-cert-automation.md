# GitLab 实体机自动证书和DNS管理方案

## 1. 创建新的IAM角色

### 1.1 创建可被实体机assume的IAM角色

```yaml
# k8s_ops/prod-infra/crossplane/infra/iam-role-gitlab-physical.yaml
apiVersion: iam.aws.crossplane.io/v1beta1
kind: Role
metadata:
  name: prod-gitlab-physical
spec:
  deletionPolicy: Delete
  forProvider:
    assumeRolePolicyDocument: |
      {
        "Version": "2012-10-17",
        "Statement": [
          {
            "Effect": "Allow",
            "Principal": {
              "AWS": "arn:aws-cn:iam::035532479701:user/gitlab-physical-user"
            },
            "Action": "sts:AssumeRole",
            "Condition": {
              "StringEquals": {
                "sts:ExternalId": "gitlab-cert-automation-2024"
              }
            }
          }
        ]
      }
    description: Role for GitLab physical server certificate automation
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: iam.aws.crossplane.io/v1beta1
kind: RolePolicyAttachment
metadata: 
  name: prod-gitlab-physical
spec: 
  deletionPolicy: Delete
  forProvider:
    roleNameRef:
      name: prod-gitlab-physical
    policyArn: arn:aws-cn:iam::035532479701:policy/prod-route53
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
```

### 1.2 创建IAM用户（用于实体机认证）

```yaml
# k8s_ops/prod-infra/crossplane/infra/iam-user-gitlab-physical.yaml
apiVersion: iam.aws.crossplane.io/v1beta1
kind: User
metadata:
  name: gitlab-physical-user
spec:
  deletionPolicy: Delete
  forProvider:
    name: gitlab-physical-user
    path: /
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: iam.aws.crossplane.io/v1beta1
kind: AccessKey
metadata:
  name: gitlab-physical-user-key
spec:
  deletionPolicy: Delete
  forProvider:
    userRef:
      name: gitlab-physical-user
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
  writeConnectionSecretsToRef:
    name: gitlab-physical-user-credentials
    namespace: infra
```

### 1.3 为用户添加AssumeRole权限

```yaml
# k8s_ops/prod-infra/crossplane/infra/iam-policy-gitlab-physical-assume.yaml
apiVersion: iam.aws.crossplane.io/v1beta1
kind: Policy
metadata:
  name: prod-gitlab-physical-assume
spec:
  deletionPolicy: Delete
  forProvider:
    document: |
      {
        "Version": "2012-10-17",
        "Statement": [
          {
            "Effect": "Allow",
            "Action": "sts:AssumeRole",
            "Resource": "arn:aws-cn:iam::035532479701:role/prod-gitlab-physical"
          }
        ]
      }
    name: prod-gitlab-physical-assume
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: iam.aws.crossplane.io/v1beta1
kind: UserPolicyAttachment
metadata:
  name: gitlab-physical-user-assume
spec:
  deletionPolicy: Delete
  forProvider:
    userRef:
      name: gitlab-physical-user
    policyArnRef:
      name: prod-gitlab-physical-assume
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
```

## 2. 实体机配置脚本

### 2.1 AWS凭证配置脚本

```bash
#!/bin/bash
# setup-aws-credentials.sh

# 从Kubernetes secret中获取AWS凭证
kubectl get secret gitlab-physical-user-credentials -n infra -o jsonpath='{.data.accessKeyId}' | base64 -d > /tmp/access_key
kubectl get secret gitlab-physical-user-credentials -n infra -o jsonpath='{.data.secretAccessKey}' | base64 -d > /tmp/secret_key

ACCESS_KEY=$(cat /tmp/access_key)
SECRET_KEY=$(cat /tmp/secret_key)

# 配置AWS CLI
aws configure set aws_access_key_id "$ACCESS_KEY" --profile gitlab-cert
aws configure set aws_secret_access_key "$SECRET_KEY" --profile gitlab-cert
aws configure set region cn-northwest-1 --profile gitlab-cert

# 清理临时文件
rm -f /tmp/access_key /tmp/secret_key

echo "AWS credentials configured for profile: gitlab-cert"
```

### 2.2 获取临时凭证的函数

```bash
#!/bin/bash
# assume-role-helper.sh

ROLE_ARN="arn:aws-cn:iam::035532479701:role/prod-gitlab-physical"
EXTERNAL_ID="gitlab-cert-automation-2024"
SESSION_NAME="gitlab-cert-$(date +%s)"

# 获取临时凭证
get_temp_credentials() {
    local credentials=$(aws sts assume-role \
        --role-arn "$ROLE_ARN" \
        --role-session-name "$SESSION_NAME" \
        --external-id "$EXTERNAL_ID" \
        --profile gitlab-cert \
        --output json)
    
    if [ $? -eq 0 ]; then
        export AWS_ACCESS_KEY_ID=$(echo $credentials | jq -r '.Credentials.AccessKeyId')
        export AWS_SECRET_ACCESS_KEY=$(echo $credentials | jq -r '.Credentials.SecretAccessKey')
        export AWS_SESSION_TOKEN=$(echo $credentials | jq -r '.Credentials.SessionToken')
        export AWS_DEFAULT_REGION="cn-northwest-1"
        
        echo "Temporary credentials obtained successfully"
        echo "Session expires at: $(echo $credentials | jq -r '.Credentials.Expiration')"
        return 0
    else
        echo "Failed to assume role"
        return 1
    fi
}

# 验证凭证
verify_credentials() {
    aws sts get-caller-identity
}

# 测试Route53权限
test_route53_access() {
    echo "Testing Route53 access..."
    aws route53 list-hosted-zones --query 'HostedZones[?Name==`rp.konvery.work.` || Name==`d.konvery.com.`]'
}
```
