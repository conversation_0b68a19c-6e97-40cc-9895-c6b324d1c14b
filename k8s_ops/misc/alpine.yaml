apiVersion: v1
kind: Pod
metadata:
  name: alpine
spec:
  # serviceAccount: gitlab
  containers:
    - image: alpine
      command:
        - "sleep"
        - "604800"
      imagePullPolicy: IfNotPresent
      name: alpine
  restartPolicy: Always

# sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
# sed -i 's/dl-cdn.alpinelinux.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apk/repositories
# echo $'https://mirrors.aliyun.com/alpine/edge/testing\n' >> /etc/apk/repositories
