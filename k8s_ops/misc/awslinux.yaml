---
apiVersion: v1
kind: Pod
metadata:
namespace: infra
labels:
  run: amz
name: amz
spec:
  serviceAccount: karpenter
  containers:
    - args:
      - sh
      - -c
      - sleep 3600
  image: public.ecr.aws/amazonlinux/amazonlinux:latest
  name: amz
  resources: {}
dnsPolicy: ClusterFirst
restartPolicy: Never

# pod启动后使用命令kubectl -n karpenter exec -it amz bash 进入pod安装aws cli 
# curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip " -o "awscliv2.zip"
# yum install -y unzip
# unzip awscliv2.zip
# ./aws/install

# 测试命令 
# aws sts get-caller-identity --debug