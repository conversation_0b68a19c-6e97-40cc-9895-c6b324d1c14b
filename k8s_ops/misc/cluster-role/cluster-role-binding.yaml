apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kvy-clusterrolebinding-admin
subjects:
  - kind: Group
    apiGroup: rbac.authorization.k8s.io
    name: kvy-clusterrole-admin
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kvy-clusterrole-admin
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kvy-clusterrolebinding-reader
subjects:
  - kind: Group
    apiGroup: rbac.authorization.k8s.io
    name: kvy-clusterrole-reader
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kvy-clusterrole-reader
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kvy-clusterrolebinding-writer
subjects:
  - kind: Group
    apiGroup: rbac.authorization.k8s.io
    name: kvy-clusterrole-writer
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kvy-clusterrole-writer
