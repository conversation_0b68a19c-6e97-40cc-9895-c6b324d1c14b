apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kvy-clusterrole-admin
rules:
  - apiGroups:
      - ""
    resources:
      - "*"
      - "pods/exec"
      - "pods/log"
    verbs:
      - "*"
  - apiGroups:
      - apps
    resources:
      - daemonsets
      - deployments
      - replicasets
      - statefulsets
    verbs:
      - "*"
  - apiGroups:
      - batch
    resources:
      - cronjobs
      - jobs
    verbs:
      - "*"
  - apiGroups:
      - apiextensions.k8s.io
      - argoproj.io
      - autoscaling
      - networking.k8s.io
      - policy
      - rbac.authorization.k8s.io
      - storage.k8s.io
      - ec2.aws.crossplane.io
    resources:
      - "*"
    verbs:
      - "*"
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kvy-clusterrole-reader
rules:
  - apiGroups:
      - ""
    resources:
      - "*"
    verbs:
      - get
      - list
  - apiGroups:
      - apps
    resources:
      - daemonsets
      - deployments
      - replicasets
      - statefulsets
    verbs:
      - get
      - list
  - apiGroups:
      - batch
    resources:
      - cronjobs
      - jobs
    verbs:
      - get
      - list
  - apiGroups:
      - apiextensions.k8s.io
      - argoproj.io
      - autoscaling
      - networking.k8s.io
      - policy
      - rbac.authorization.k8s.io
      - storage.k8s.io
    resources:
      - "*"
    verbs:
      - get
      - list
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kvy-clusterrole-writer
rules:
  - apiGroups:
      - ""
    resources:
      - "*"
    verbs:
      - "*"
  - apiGroups:
      - apps
    resources:
      - daemonsets
      - deployments
      - replicasets
      - statefulsets
    verbs:
      - "*"
  - apiGroups:
      - batch
    resources:
      - cronjobs
      - jobs
    verbs:
      - "*"
  - apiGroups:
      - apiextensions.k8s.io
      - argoproj.io
      - autoscaling
      - networking.k8s.io
      - policy
      - rbac.authorization.k8s.io
      - storage.k8s.io
    resources:
      - "*"
    verbs:
      - "*"
