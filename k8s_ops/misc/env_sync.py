#!bin/python3

import argparse
import logging
import os
import subprocess
import re
import fileinput

env_ctx = {
    "dev": {
        "k8s_ctx": "non-prod-workload",
        "prefix": "",
        "root_path": "../non-prod-workload/argocd/konvery",
    },
    "test": {
        "k8s_ctx": "non-prod-workload",
        "prefix": "staging-",
        "root_path": "../non-prod-workload/argocd/staging/konvery",
    },
    "prod": {
        "k8s_ctx": "prod-workload",
        "prefix": "",
        "root_path": "../prod-workload/argocd/konvery",
    },
}

app_revs = {
    "admin-dashboard": "0.0.0",
    "label-tool": "0.0.0",
    "iam": "0.0.0",
    "sansheng-anno": "0.0.0",
    "sansheng-annofeed": "0.0.0",
    "sansheng-annostat": "0.0.0",
    "sansheng-annout": "0.0.0",
}

DEFAULT_SOURCE_ENV = "dev"
DEFAULT_TARGET_ENV = "test"


def switch_k8s_ctx(ctx):
    logging.info(f"Switching k8s context: {ctx}")
    result = subprocess.run(["kubectx", env_ctx[ctx]["k8s_ctx"]], capture_output=True)

    if result.returncode != 0:
        logging.error(result.stderr.decode())
    logging.info(result.stdout.decode())


def retrieve_source_env(source_env_name):
    logging.info(f"Retrieving source env: {source_env_name}")
    switch_k8s_ctx(source_env_name)

    for app_name in app_revs.keys():
        logging.info(
            f"Retrieving source env app: {env_ctx[source_env_name]['prefix'] + app_name}"
        )
        result = subprocess.run(
            [
                "kubectl",
                "get",
                "-n",
                "argocd",
                "application.argoproj.io",
                env_ctx[source_env_name]["prefix"] + app_name,
                "-o",
                "custom-columns=:.status.sync.revision",
                "--no-headers",
            ],
            capture_output=True,
        )

        if result.returncode != 0:
            logging.error(result.stderr.decode())

        app_revs[app_name] = result.stdout.decode().strip()

    logging.info(app_revs[app_name])
    return app_revs


def find_and_replace(filename, text_to_search, replacement_text):
    logging.info(f"processing file: {filename}")
    with fileinput.FileInput(filename, inplace=True) as file:
        for line in file:
            print(re.sub(text_to_search, replacement_text, line), end="")


def sync_target_env(target_env_name, app_revs):
    logging.info(f"Syncing target env: {target_env_name}")
    logging.info(f"Using rev information: {app_revs}")
    switch_k8s_ctx(target_env_name)

    for app_name in app_revs.keys():
        find_and_replace(
            os.path.join(env_ctx[target_env_name]["root_path"], app_name + ".yaml"),
            r"targetRevision: ([0-9\.]+)",
            "targetRevision: " + app_revs[app_name],
        )

    logging.info(f"Applying changes to target env: {target_env_name}")
    result = subprocess.run(
        ["kubectl", "apply", "-f", env_ctx[target_env_name]["root_path"]],
        capture_output=True,
    )

    if result.returncode != 0:
        logging.error(result.stderr.decode())

    logging.warning("Successfully synced!")


if __name__ == "__main__":
    ap = argparse.ArgumentParser()

    ap.add_argument(
        "-d", "--debug", action="store_true", help="debug flag, default: false"
    )
    ap.add_argument(
        "-s",
        "--source",
        default=DEFAULT_SOURCE_ENV,
        help="Environment to sync from",
    )
    ap.add_argument(
        "-t",
        "--target",
        default=DEFAULT_TARGET_ENV,
        help="Environment to sync to",
    )
    args = ap.parse_args()

    if args.debug:
        logging.basicConfig(level=logging.INFO)
        # logging.basicConfig(level=logging.DEBUG)

    app_revs = retrieve_source_env(args.source)
    sync_target_env(args.target, app_revs)
