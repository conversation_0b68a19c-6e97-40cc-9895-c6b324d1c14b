apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: kong
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: sansheng
  destination:
    namespace: sansheng
    server: "https://kubernetes.default.svc"
  source:
    repoURL: "https://charts.bitnami.com/bitnami"
    targetRevision: "6.3.11"
    chart: kong
    helm:
      values: |
        postgresql:
          enabled: false
          external:
            host: non-prod-workload-postgresql.cluster-ck7i7eqx0frz.rds.cn-northwest-1.amazonaws.com.cn
            existingSecret: kong-external-secret
            existingSecretPasswordKey: password
  syncPolicy:
    automated:
      selfHeal: true
      prune: true
    syncOptions:
      - CreateNamespace=true
  ignoreDifferences:
    - kind: ClusterRole
      jsonPointers:
        - /rules
