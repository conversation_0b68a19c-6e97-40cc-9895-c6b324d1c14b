apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: external-dns-internal
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: infra
  destination:
    namespace: infra
    server: 'https://kubernetes.default.svc'
  source:
    repoURL: 'https://charts.bitnami.com/bitnami'
    targetRevision: '6.9.0'
    chart: external-dns
    helm:
      values: |
        domainFilters:
        - np.konvery.work
        sources:
          - service
          - ingress
        aws:
          region: cn-northwest-1
          zoneType: private
        serviceAccount:
          name: external-dns-internal
          annotations:
            eks.amazonaws.com/role-arn: arn:aws-cn:iam::************:role/non-prod-externaldns
        provider: aws
        annotationFilter: external-dns-annotation in (ingress-nginx-internal)
        policy: sync
        txtOwnerId: non-prod-infra-internal
  syncPolicy:
    automated:
      selfHeal: true
      prune: true
    syncOptions:
    - CreateNamespace=true
