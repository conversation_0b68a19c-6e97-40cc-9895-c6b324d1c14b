apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: gitlab-runner
  namespace: argocd
spec:
  destination:
    namespace: infra
    server: https://kubernetes.default.svc
  project: infra
  source:
    chart: gitlab-runner
    helm:
      values: |
        affinity: {}
        checkInterval: 3
        concurrent: 10
        gitlabUrl: https://gitlab.np.konvery.work/
        hostAliases: []
        # hpa:
        #   maxReplicas: 30
        #   metrics:
        #   - pods:
        #       metricName: gitlab_runner_jobs
        #       targetAverageValue: 100m
        #     type: Pods
        #   minReplicas: 6
        # hpa:
        #   maxReplicas: 30
        #   metrics:
        #   - pods:
        #       metricName: gitlab_runner_jobs
        #       targetAverageValue: 100m
        #     type: Pods
        #   minReplicas: 6
        imagePullPolicy: IfNotPresent
        metrics:
          enabled: true
        rbac:
          create: true
          serviceAccountAnnotations:
            eks.amazonaws.com/role-arn: arn:aws-cn:iam::************:role/non-prod-gitlab
        runnerRegistrationToken: dhxvitEEqsdYMc54dsEds3rBby1rarMNSHg7D4hkxVBkmbXp4rKLYdiLvu648Q6N
        runners:
          config: |
            [[runners]]
              [runners.kubernetes]
                namespace = "infra"
                image = "ubuntu:20.04"
                privileged = true
              [[runners.kubernetes.volumes.empty_dir]]
                name = "docker-certs"
                mount_path = "/certs/client"
                medium = "Memory"
              [runners.cache]
                Type = "s3"
                Path = "path/runner"
                Shared = false
                [runners.cache.s3]
                  BucketName = "non-prod-infra-gitlab-tmp"
                  BucketLocation = "cn-northwest-1"
                  Insecure = false
                  AuthenticationType = "iam"
          locked: false
          runUntagged: true
          tags: k8s,runner
        securityContext:
          # fsGroup: 65533
          # fsGroup: 65533
          runAsUser: 100
        unregisterRunner: true
        terminationGracePeriodSeconds: 3600
    repoURL: https://charts.gitlab.io/
    targetRevision: 0.61.0
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
