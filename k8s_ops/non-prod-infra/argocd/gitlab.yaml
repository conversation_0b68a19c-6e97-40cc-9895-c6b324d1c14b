---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: gitlab
  namespace: argocd
spec:
  destination:
    namespace: infra
    server: https://kubernetes.default.svc
  project: infra
  source:
    chart: gitlab
    helm:
      valuesObject:
        affinity:
          nodeAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
                - matchExpressions:
                    - key: eks.amazonaws.com/capacityType
                      operator: In
                      values:
                        - ON_DEMAND
        certmanager:
          install: false
        gitlab:
          migrations:
            image:
              repository: registry.gitlab.com/gitlab-org/build/cng/gitlab-toolbox-ce
          sidekiq:
            registry:
              enabled: false
            resources:
              requests:
                cpu: 450m
          toolbox:
            backups:
              objectStorage:
                config:
                  key: config
                  secret: gitlab-s3cmd-config
        gitlab-runner:
          install: false
        global:
          appConfig:
            artifacts:
              bucket: non-prod-infra-gitlab-artifacts
            backups:
              bucket: non-prod-infra-gitlab-backup
              tmpBucket: non-prod-infra-gitlab-tmp
            lfs:
              bucket: non-prod-infra-gitlab-lfs
            object_store:
              connection:
                key: connection
                secret: gitlab-rails-storage
              enabled: true
              proxy_download: true
              storage_options:
                server_side_encryption: AES256
            omniauth:
              allowSingleSignOn: true
              # disable this to enable root login, and assign the permission to users. Then enable this
              # autoSignInWithProvider: openid_connect
              blockAutoCreatedUsers: false
              enabled: true
              providers:
                - secret: gitlab-omniauth-keycloak-provider
            packages:
              bucket: non-prod-infra-gitlab-packages
            uploads:
              bucket: non-prod-infra-gitlab-uploads
          edition: ce
          hosts:
            domain: np.konvery.work
            https: true
          ingress:
            annotations:
              external-dns-annotation: ingress-nginx-internal
            class: ingress-nginx-internal
            configureCertmanager: false
            tls:
              enabled: true
              secretName: wildcard-np-konvery-work
          kas:
            enabled: false
          minio:
            enabled: false
          psql:
            database: gitlab
            host: non-*********************.cluster-ck7i7eqx0frz.rds.cn-northwest-1.amazonaws.com.cn
            password:
              key: password
              secret: gitlab-role-secret
            port: 5432
            username: gitlab
          registry:
            bucket: non-prod-infra-gitlab-registry
          serviceAccount:
            annotations:
              eks.amazonaws.com/role-arn: arn:aws-cn:iam::************:role/non-prod-gitlab
            create: true
            enabled: true
        nginx-ingress:
          enabled: false
        postgresql:
          install: false
        prometheus:
          install: false
        registry:
          enabled: false
        upgradeCheck:
          enabled: false
    repoURL: https://charts.gitlab.io/
    targetRevision: 7.5.7
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
