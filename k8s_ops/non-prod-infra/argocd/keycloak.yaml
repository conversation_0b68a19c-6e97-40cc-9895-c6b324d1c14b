---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: keycloak
  namespace: argocd
spec:
  destination:
    namespace: infra
    server: https://kubernetes.default.svc
  project: infra
  source:
    chart: keycloak
    helm:
      values: |
        affinity:
          nodeAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
                - matchExpressions:
                    - key: eks.amazonaws.com/capacityType
                      operator: In
                      values:
                        - ON_DEMAND
        # auth:
        #   adminPasword: D6U5Me680YfBShmr
        #   adminUser: admin
        externalDatabase:
          existingSecret: keycloak-db-secret
          existingSecretDatabaseKey: POSTGRES_DATABASE
          existingSecretHostKey: POSTGRES_EXTERNAL_ADDRESS
          existingSecretPasswordKey: POSTGRES_PASSWORD
          existingSecretPortKey: POSTGRES_EXTERNAL_PORT
          existingSecretUserKey: POSTGRES_USERNAME
        ingress:
          annotations:
            external-dns-annotation: ingress-nginx-internal
          enabled: true
          hostname: keycloak.np.konvery.work
          ingressClassName: ingress-nginx-internal
          extraTls:
          - hosts:
            - keycloak.np.konvery.work
            secretName: wildcard-np-konvery-work
        postgresql:
          enabled: false
        prouduction: true
        tls:
          autoGenerated: true
          enabled: true
    repoURL: https://charts.bitnami.com/bitnami
    targetRevision: 15.1.8
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
