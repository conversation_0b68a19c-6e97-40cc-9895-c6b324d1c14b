---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: loki
  namespace: argocd
spec:
  destination:
    namespace: monitoring
    server: https://kubernetes.default.svc
  project: infra
  source:
    chart: loki-stack
    helm:
      valuesObject:
        loki:
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                      - key: partition
                        operator: In
                        values:
                          - monitoring
          config:
            compactor:
              retention_enabled: true
          ingress:
            annotations:
              external-dns-annotation: ingress-nginx-internal
              nginx.ingress.kubernetes.io/backend-protocol: HTTPS
              nginx.ingress.kubernetes.io/ssl-passthrough: "true"
            enabled: false
            hosts:
              - host: loki.np.konvery.work
                paths:
                  - /
            ingressClassName: ingress-nginx-internal
            tls:
              - hosts:
                  - loki.np.konvery.work
                secretName: wildcard-np-konvery-work
          persistence:
            enabled: true
          tolerations:
            - effect: NoSchedule
              key: monitoring
              operator: Exists
        promtail:
          enabled: true
          tolerations:
            - effect: NoSchedule
              key: monitoring
              operator: Exists
    repoURL: https://grafana.github.io/helm-charts
    targetRevision: 2.9.11
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true