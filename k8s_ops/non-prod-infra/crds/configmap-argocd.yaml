---
apiVersion: v1
data:
  oidc.config: |
    name: Keycloak
    issuer: https://keycloak.np.konvery.work/realms/konvery
    clientID: argocd
    clientSecret: XP81e1FQDMrQMHEdWqHX4xLmzra9gqBf
    requestedScopes: ["openid", "profile", "email", "groups"]
    # requestedScopes: ["roles", "profile", "email", "groups"]
  resource.compareoptions: |
    ignoreAggregatedRoles: true
  url: https://argocd.np.konvery.work
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/name: argocd-cm
    app.kubernetes.io/part-of: argocd
  name: argocd-cm
  namespace: argocd
    # requestedScopes: ["roles", "profile", "email", "groups"]
---
apiVersion: v1
data:
  server.insecure: "false"
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/name: argocd-cmd-params-cm
    app.kubernetes.io/part-of: argocd
  name: argocd-cmd-params-cm
  namespace: argocd
---
apiVersion: v1
data:
  policy.csv: |
    p, role:editor, applycations, *, */*, allow
    p, role:editor, clusters, get, *, allow
    p, role:editor, repositories, get, *, allow
    p, role:editor, repositories, create, *, allow
    p, role:editor, repositories, update, *, allow
    p, role:editor, repositories, delete, *, allow
    p, role:editor, logs, get, *, allow
    p, role:editor, exec, create, */*, allow
    p, role:editor, projects, get, *, allow
    g, kvy-admin, role:admin
    g, kvy-editor, role:editor
    g, kvy-viewer, role:readonly
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/name: argocd-rbac-cm
    app.kubernetes.io/part-of: argocd
  name: argocd-rbac-cm
  namespace: argocd
