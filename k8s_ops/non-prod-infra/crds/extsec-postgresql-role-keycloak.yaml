apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: keycloak-db-secret
  namespace: infra
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: external-secrets
    kind: ClusterSecretStore
  target:
    name: keycloak-db-secret
    creationPolicy: Owner
    template: 
      data:
        POSTGRES_DATABASE: keycloak
        POSTGRES_EXTERNAL_ADDRESS: "{{ .endpoint | toString }}"
        POSTGRES_EXTERNAL_PORT: "5432"
        POSTGRES_PASSWORD: "{{ .password | toString }}"
        POSTGRES_USERNAME: "{{ .username | toString }}"
  dataFrom:
    - extract:
        key: non-prod-infra-postgresql-role-keycloak