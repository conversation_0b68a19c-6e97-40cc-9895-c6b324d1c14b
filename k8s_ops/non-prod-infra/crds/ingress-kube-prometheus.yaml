apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    # kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/backend-protocol: HTTP
    nginx.ingress.kubernetes.io/ssl-passthrough: "true"
    external-dns-annotation: ingress-nginx-internal
  name: prometheus
  namespace: monitoring
spec:
  ingressClassName: ingress-nginx-internal
  rules:
  - host: prometheus.np.konvery.work
    http:
      paths:
      - backend:
          service: 
            name: prometheus-k8s
            port: 
              name: web
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - prometheus.np.konvery.work
    secretName: wildcard-np-konvery-work
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    # kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/backend-protocol: HTTP
    nginx.ingress.kubernetes.io/ssl-passthrough: "true"
    external-dns-annotation: ingress-nginx-internal
  name: grafana
  namespace: monitoring
spec:
  ingressClassName: ingress-nginx-internal
  rules:
  - host: grafana.np.konvery.work
    http:
      paths:
      - backend:
          service: 
            name: grafana
            port: 
              name: http
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - grafana.np.konvery.work
    secretName: wildcard-np-konvery-work
