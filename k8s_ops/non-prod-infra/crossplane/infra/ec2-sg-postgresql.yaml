apiVersion: ec2.aws.crossplane.io/v1beta1
kind: SecurityGroup
metadata: 
  name: non-prod-infra-postgresql-sg
spec:
  deletionPolicy: Delete
  forProvider:
    description: Security group for database in non-prod infra vpc
    groupName: non-prod-infra-postgresql-sg
    region: cn-northwest-1
    ingress: 
      - fromPort: 5432
        toPort: 5432
        ipProtocol: TCP
        userIdGroupPairs:
          - groupId: "sg-05d433b65431a274e"
            description: "Allow all eks workers to access database"
    vpcId: vpc-0df4efad825002e12 # non-prod-infra vpc id
    tags:
      - key: Name
        value: non-prod-infra-postgresql-sg
      - key: owner
        value: sre
      - key: profile
        value: non-prod
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
