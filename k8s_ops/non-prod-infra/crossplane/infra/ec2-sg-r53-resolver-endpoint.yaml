apiVersion: ec2.aws.crossplane.io/v1beta1
kind: SecurityGroup
metadata:
  name: nonprod-infra-r53-resolver-endpoint-sg
spec:
  forProvider:
    region: cn-northwest-1
    vpcId: vpc-0df4efad825002e12 # non-prod-infra vpc id
    groupName: nonprod-infra-r53-resolver-endpoint-sg
    description: For vpn access to internal network
    ingress:
      - fromPort: 80
        toPort: 80
        ipProtocol: tcp
        ipRanges:
          - cidrIp: 0.0.0.0/0
            description: http
      - fromPort: 443
        toPort: 443
        ipProtocol: tcp
        ipRanges:
          - cidrIp: 0.0.0.0/0
            description: https
      - fromPort: 53
        toPort: 53
        ipProtocol: udp
        ipRanges:
          - cidrIp: 0.0.0.0/0
            description: dns
    tags:
      - key: Name
        value: nonprod-infra-r53-resolver-endpoint-sg
      - key: Controller
        value: crossplane
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config