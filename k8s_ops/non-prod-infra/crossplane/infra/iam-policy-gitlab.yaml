apiVersion: iam.aws.crossplane.io/v1beta1
kind: Policy
metadata:
  name: non-prod-gitlab
spec:
  deletionPolicy: Delete
  forProvider:
    document: |
      {
          "Statement": [
              {
                  "Action": [
                      "s3:DeleteObject",
                      "s3:DeleteObjectVersion",
                      "s3:GetObject",
                      "s3:ListMultipartUploadParts",
                      "s3:PutObject",
                      "s3:PutObjectAcl",
                      "s3:AbortMultipartUpload"
                  ],
                  "Effect": "Allow",
                  "Resource": "arn:aws-cn:s3:::non-prod-infra-gitlab*/*",
                  "Sid": "objectPermission"
              },
              {
                  "Action": [
                      "s3:ListAllMyBuckets",
                      "s3:ListJobs",
                      "s3:ListStorageLensConfigurations"
                  ],
                  "Effect": "Allow",
                  "Resource": "*",
                  "Sid": "allPermission"
              },
              {
                  "Action": [
                      "s3:ListBucket",
                      "s3:GetBucketLocation",
                      "s3:ListBucketVersions",
                      "s3:AbortMultipartUpload",
                      "s3:ListMultipartUploadParts",
                      "s3:ListBucketMultipartUploads"
                  ],
                  "Effect": "Allow",
                  "Resource": "arn:aws-cn:s3:::non-prod-infra-gitlab*",
                  "Sid": "bucketPermission"
              }
          ],
          "Version": "2012-10-17"
      }
    name: non-prod-gitlab
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
