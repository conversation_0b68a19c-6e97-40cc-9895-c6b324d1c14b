apiVersion: iam.aws.crossplane.io/v1beta1
kind: Policy
metadata:
  name: non-prod-secret-manager
spec:
  deletionPolicy: Delete
  forProvider:
    document: |
      {
          "Version": "2012-10-17",
          "Statement": [
              {
                  "Effect": "Allow",
                  "Action": [
                      "secretsmanager:GetResourcePolicy",
                      "secretsmanager:GetSecretValue",
                      "secretsmanager:DescribeSecret",
                      "secretsmanager:ListSecretVersionIds"
                  ],
                  "Resource": "arn:aws-cn:secretsmanager:*:*:secret:*"
              }
          ]
      }
    name: non-prod-secret-manager
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
