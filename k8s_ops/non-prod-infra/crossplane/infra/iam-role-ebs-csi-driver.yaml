apiVersion: iam.aws.crossplane.io/v1beta1
kind: Role
metadata:
  name: non-prod-infra-aws-ebs-csi-driver
spec:
  deletionPolicy: Delete
  forProvider:
    assumeRolePolicyDocument: |
      {
        "Version": "2012-10-17",
        "Statement": [
          {
            "Effect": "Allow",
            "Action": "sts:AssumeRoleWithWebIdentity",
            "Principal": {
              "Federated": "arn:aws-cn:iam::************:oidc-provider/oidc.eks.cn-northwest-1.amazonaws.com.cn/id/B70BCA1E4F199729248B683E13A0AED7"
            },
            "Condition": {
              "StringEquals": {
                "oidc.eks.cn-northwest-1.amazonaws.com.cn/id/B70BCA1E4F199729248B683E13A0AED7:sub": "system:serviceaccount:kube-system:ebs-csi-controller-sa"
              }
            }
          }
        ]
      }
    description: aws-ebs-csi-driver to be used in non-prod-infra eks cluster
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
---
apiVersion: iam.aws.crossplane.io/v1beta1
kind: RolePolicyAttachment
metadata: 
  name: non-prod-infra-aws-ebs-csi-driver
spec: 
  deletionPolicy: Delete
  forProvider:
    roleNameRef:
      name: non-prod-infra-aws-ebs-csi-driver
    policyArn: arn:aws-cn:iam::aws:policy/service-role/AmazonEBSCSIDriverPolicy
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config