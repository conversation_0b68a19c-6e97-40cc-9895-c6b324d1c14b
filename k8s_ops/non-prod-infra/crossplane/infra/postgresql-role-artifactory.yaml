---
apiVersion: postgresql.sql.crossplane.io/v1alpha1
kind: Role
metadata:
  name: artifactory
spec:
  deletionPolicy: Delete
  forProvider:
    connectionLimit: 100
    privileges:
      login: true
      createDb: true
      inherit: true
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-postgresql-config
  writeConnectionSecretToRef:
    name: artifactory-role-secret
    namespace: infra
