---
apiVersion: postgresql.sql.crossplane.io/v1alpha1
kind: Role
metadata:
  name: gitlab
spec:
  deletionPolicy: Delete
  forProvider:
    connectionLimit: 100
    privileges:
      login: true
      createDb: true
      inherit: true
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-postgresql-config
  writeConnectionSecretToRef:
    name: gitlab-role-secret
    namespace: infra
---
apiVersion: postgresql.sql.crossplane.io/v1alpha1
kind: Grant
metadata:
  name: gitlab-grant-role-membership
spec:
  forProvider:
    withOption: ADMIN
    roleRef:
      name: gitlab
    memberOf: rds_superuser
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-postgresql-config
