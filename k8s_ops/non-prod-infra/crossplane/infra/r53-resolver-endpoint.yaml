apiVersion: route53resolver.aws.crossplane.io/v1alpha1
kind: ResolverEndpoint
metadata: 
  name: non-prod-infra-route53-resolver
spec: 
  deletionPolicy: Delete
  forProvider:
    name: non-prod-infra-route53-resolver
    direction: INBOUND
    ipAddresses:
      - ip: ************
        subnetId: subnet-0ed4ec51369b0ec27
      - ip: ************
        subnetId: subnet-07f79af33d9b64c67
      - ip: ************
        subnetId: subnet-03db421791ebfdc82
    region: cn-northwest-1
    securityGroupIdRefs:
      - name: nonprod-infra-r53-resolver-endpoint-sg
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
