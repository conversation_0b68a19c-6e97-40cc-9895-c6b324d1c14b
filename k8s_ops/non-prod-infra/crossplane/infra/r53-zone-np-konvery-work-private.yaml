apiVersion: route53.aws.crossplane.io/v1alpha1
kind: HostedZone
metadata: 
  name: np-konvery-work
spec: 
  deletionPolicy: Delete
  forProvider:
    name: np.konvery.work
    config:
      privateZone: true
    vpc:
      vpcId: vpc-0df4efad825002e12
      # vpcIdSelector:
      #   matchLabels:
      #     name: non-prod-infra
      vpcRegion: cn-northwest-1
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
