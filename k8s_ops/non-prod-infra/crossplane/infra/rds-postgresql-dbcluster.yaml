---
apiVersion: rds.aws.crossplane.io/v1alpha1
kind: DBCluster
metadata:
  name: non-prod-infra-postgresql
spec:
  deletionPolicy: Delete
  forProvider:
    backupRetentionPeriod: 1
    dbSubnetGroupName: non-prod-infra
    deletionProtection: true
    engine: aurora-postgresql
    engineVersion: "14.5"
    finalDBSnapshotIdentifier: non-prod-infra-postgresql
    masterUserPasswordSecretRef:
      key: password
      name: manual-initial-db-pw
      namespace: crossplane-system
    masterUsername: sqladmin
    port: 5432
    preferredBackupWindow: 16:00-17:00
    preferredMaintenanceWindow: Sat:20:00-Sat:21:00
    region: cn-northwest-1
    skipFinalSnapshot: false
    storageEncrypted: true
    vpcSecurityGroupIDRefs:
      - name: non-prod-infra-postgresql-sg
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
  writeConnectionSecretToRef:
    name: non-prod-infra-postgresql
    namespace: crossplane-system
