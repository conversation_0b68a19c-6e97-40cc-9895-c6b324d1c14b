apiVersion: secretsmanager.aws.crossplane.io/v1beta1
kind: Secret
metadata:
  name: non-prod-keycloak-client-credential-gitlab
spec:
  forProvider:
    region: cn-northwest-1
    description: "non-prod KeyCloak gitlab client token"
    forceDeleteWithoutRecovery: true
    #recoveryWindowInDays: 7
    stringSecretRef:
      name: keycloak-client-secret-gitlab
      namespace: infra
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config