# apiVersion: secretsmanager.aws.crossplane.io/v1beta1
# kind: Secret
# metadata:
#   name: non-prod-infra-postgresql-role-artifactory
# spec:
#   forProvider:
#     region: cn-northwest-1
#     description: "artifactory db cluster's secret"
#     forceDeleteWithoutRecovery: true
#     #recoveryWindowInDays: 7
#     stringSecretRef:
#       name: artifactory-role-secret
#       namespace: infra
#   providerConfigRef:
#     name: non-prod-infra-crossplane-provider-aws-config
