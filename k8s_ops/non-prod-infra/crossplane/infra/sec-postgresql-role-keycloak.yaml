# apiVersion: secretsmanager.aws.crossplane.io/v1beta1
# kind: Secret
# metadata:
#   name: non-prod-infra-postgresql-role-keycloak
# spec:
#   forProvider:
#     region: cn-northwest-1
#     description: "keycloak db cluster's secret"
#     forceDeleteWithoutRecovery: true
#     #recoveryWindowInDays: 7
#     stringSecretRef:
#       name: keycloak-role-secret
#       namespace: infra
#   providerConfigRef:
#     name: non-prod-infra-crossplane-provider-aws-config
