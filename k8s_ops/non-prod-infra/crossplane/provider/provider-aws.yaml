---
apiVersion: pkg.crossplane.io/v1alpha1
kind: ControllerConfig
metadata:
  name: non-prod-infra-crossplane-controllerconfig
  annotations:
    eks.amazonaws.com/role-arn: arn:aws-cn:iam::035532479701:role/non-prod-infra-crossplane-provider
spec:
  podSecurityContext:
    fsGroup: 2000
  securityContext:
    runAsUser: 0
---
apiVersion: pkg.crossplane.io/v1
kind: Provider
metadata:
  name: non-prod-infra-crossplane-provider
spec:
  package: xpkg.upbound.io/crossplane-contrib/provider-aws:v0.41.1
  controllerConfigRef:
    name: non-prod-infra-crossplane-controllerconfig
---
apiVersion: aws.crossplane.io/v1beta1
kind: ProviderConfig
metadata:
  name: non-prod-infra-crossplane-provider-aws-config
spec:
  credentials:
    source: InjectedIdentity
