apiVersion: pkg.crossplane.io/v1alpha1
kind: ControllerConfig
metadata:
  name: provider-sql
spec:
  args:
    - "--debug"
  env:
  - name: PGDATABASE
    value: postgres
---
apiVersion: pkg.crossplane.io/v1
kind: Provider
metadata:
  name: provider-sql
spec:
  package: crossplane/provider-sql:v0.7.0
  controllerConfigRef:
    name: provider-sql
---
apiVersion: postgresql.sql.crossplane.io/v1alpha1
kind: ProviderConfig
metadata:
  name: non-prod-infra-crossplane-provider-postgresql-config
spec:
  credentials:
    source: PostgreSQLConnectionSecret
    connectionSecretRef:
      namespace: crossplane-system
      name: non-prod-infra-postgresql
  sslMode: disable
