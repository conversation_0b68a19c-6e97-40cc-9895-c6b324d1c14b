apiVersion: ec2.aws.crossplane.io/v1beta1
kind: SecurityGroup
metadata: 
  name: non-prod-workload-postgresql-sg
spec:
  deletionPolicy: Delete
  forProvider:
    description: Security group for database in non-prod workload vpc
    groupName: non-prod-workload-postgresql-sg
    region: cn-northwest-1
    ingress: 
      - fromPort: 5432
        toPort: 5432
        ipProtocol: TCP
        userIdGroupPairs:
          - groupId: "sg-0fb1a3ccd98c7dd8b"
            description: "Allow all workload eks workers to access database"
    vpcId: vpc-0c6868740b8ec800a # non-prod-workload vpc id
    tags:
      - key: Name
        value: non-prod-workload-postgresql-sg
      - key: owner
        value: sre
      - key: profile
        value: non-prod
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
