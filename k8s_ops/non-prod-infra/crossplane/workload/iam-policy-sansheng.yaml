apiVersion: iam.aws.crossplane.io/v1beta1
kind: Policy
metadata:
  name: non-prod-sansheng
spec:
  deletionPolicy: Delete
  forProvider:
    document: |
      {
        "Statement": [
          {
            "Action": [
                "s3:DeleteObject",
                "s3:DeleteObjectVersion",
                "s3:GetObject",
                "s3:ListMultipartUploadParts",
                "s3:PutObject"
            ],
            "Effect": "Allow",
            "Resource": "arn:aws-cn:s3:::non-prod-workload-sansheng/*",
            "Sid": "objectPermission"
          },
          {
            "Action": [
                "s3:ListAllMyBuckets",
                "s3:ListJobs",
                "s3:ListStorageLensConfigurations"
            ],
            "Effect": "Allow",
            "Resource": "*",
            "Sid": "allPermission"
          },
          {
            "Action": [
                "s3:ListBucket",
                "s3:ListBucketMultipartUploads",
                "s3:ListBucketVersions"
            ],
            "Effect": "Allow",
            "Resource": "arn:aws-cn:s3:::non-prod-workload-sansheng",
            "Sid": "bucketPermission"
          },
          {
            "Action": "sqs:*",
            "Effect": "Allow",
            "Resource": [
              "arn:aws-cn:sqs:cn-northwest-1:035532479701:non-prod-workload-sqs-sansheng",
              "arn:aws-cn:sqs:cn-northwest-1:035532479701:non-prod-workload-sqs-sansheng-anno",
              "arn:aws-cn:sqs:cn-northwest-1:035532479701:non-prod-workload-sqs-sansheng-annofeed",
              "arn:aws-cn:sqs:cn-northwest-1:035532479701:non-prod-workload-sqs-sansheng-annostat",
              "arn:aws-cn:sqs:cn-northwest-1:035532479701:non-prod-workload-sqs-sansheng-staging",
              "arn:aws-cn:sqs:cn-northwest-1:035532479701:non-prod-workload-sqs-sansheng-anno-staging",
              "arn:aws-cn:sqs:cn-northwest-1:035532479701:non-prod-workload-sqs-sansheng-annofeed-staging",
              "arn:aws-cn:sqs:cn-northwest-1:035532479701:non-prod-workload-sqs-sansheng-annostat-staging"
            ]
          },
          { 
            "Action": "sns:*",
            "Effect": "Allow",
            "Resource": [
              "arn:aws-cn:sns:cn-northwest-1:035532479701:non-prod-workload-sns-sansheng",
              "arn:aws-cn:sns:cn-northwest-1:035532479701:non-prod-workload-sns-sansheng-anno",
              "arn:aws-cn:sns:cn-northwest-1:035532479701:non-prod-workload-sns-sansheng-staging",
              "arn:aws-cn:sns:cn-northwest-1:035532479701:non-prod-workload-sns-sansheng-anno-staging"
            ]
          },
          {
            "Action": "rds:*",
            "Effect": "Allow",
            "Resource": "arn:aws-cn:rds:cn-northwest-1:035532479701:cluster:non-prod-workload-postgresql"
          }
        ],
        "Version": "2012-10-17"
      }
    name: non-prod-sansheng
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
