apiVersion: iam.aws.crossplane.io/v1beta1
kind: Role
metadata:
  name: non-prod-workload-externalsecret
spec:
  deletionPolicy: Delete
  forProvider:
    assumeRolePolicyDocument: |
      {
        "Version": "2012-10-17",
        "Statement": [
          {
            "Effect": "Allow",
            "Action": "sts:AssumeRoleWithWebIdentity",
            "Principal": {
              "Federated": "arn:aws-cn:iam::************:oidc-provider/oidc.eks.cn-northwest-1.amazonaws.com.cn/id/800D32D9C2C460A1298FAAD150156907"
            },
            "Condition": {
              "StringEquals": {
                "oidc.eks.cn-northwest-1.amazonaws.com.cn/id/800D32D9C2C460A1298FAAD150156907:sub": "system:serviceaccount:infra:external-secrets"
              }
            }
          }
        ]
      }
    description: external-secrets to be used in non-prod-workload eks cluster
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
---
apiVersion: iam.aws.crossplane.io/v1beta1
kind: RolePolicyAttachment
metadata: 
  name: non-prod-workload-externalsecret
spec: 
  deletionPolicy: Delete
  forProvider:
    roleNameRef:
      name: non-prod-workload-externalsecret
    policyArn: arn:aws-cn:iam::************:policy/non-prod-secret-manager
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config