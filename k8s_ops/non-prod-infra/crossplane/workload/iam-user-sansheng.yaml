apiVersion: iam.aws.crossplane.io/v1beta1
kind: User
metadata:
  name: non-prod-workload-sansheng
spec:
  deletionPolicy: Delete
  forProvider:
    tags:
      - key: usage
        value: used to created presigned S3 URL
      - key: engine
        value: crossplane
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
---
apiVersion: iam.aws.crossplane.io/v1beta1
kind: UserPolicyAttachment
metadata: 
  name: non-prod-workload-sansheng
spec: 
  deletionPolicy: Delete
  forProvider:
    userNameRef:
      name: non-prod-workload-sansheng
    policyArn: arn:aws-cn:iam::035532479701:policy/non-prod-sansheng
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
---
apiVersion: iam.aws.crossplane.io/v1beta1
kind: AccessKey
metadata:
  name: sansheng-iam-s3
spec:
  forProvider:
    userNameRef:
      name: non-prod-workload-sansheng
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
  writeConnectionSecretToRef:
    name: sansheng-iam-s3
    namespace: crossplane-system
