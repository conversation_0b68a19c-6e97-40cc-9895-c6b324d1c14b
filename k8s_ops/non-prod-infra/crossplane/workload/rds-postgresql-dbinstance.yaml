---
apiVersion: rds.aws.crossplane.io/v1alpha1
kind: DBInstance
metadata:
  name: non-prod-workload-postgresql-instance
spec:
  forProvider:
    autoMinorVersionUpgrade: true
    applyImmediately: true
    dbClusterIdentifier: non-prod-workload-postgresql
    dbInstanceClass: db.t4g.medium
    dbParameterGroupName: default.aurora-postgresql14
    dbSubnetGroupName: non-prod-workload
    enablePerformanceInsights: true
    engine: aurora-postgresql
    engineVersion: "14.5"
    licenseModel: postgresql-license
    promotionTier: 1
    publiclyAccessible: false
    region: cn-northwest-1
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
