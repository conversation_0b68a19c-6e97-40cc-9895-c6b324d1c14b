---
apiVersion: s3.aws.crossplane.io/v1beta1
kind: Bucket
metadata:
  name: non-prod-workload-annomaker
spec:
  deletionPolicy: Delete
  forProvider:
    acl: private
    lifecycleConfiguration:
      rules:
        - abortIncompleteMultipartUpload:
            daysAfterInitiation: 7
          status: Enabled
    locationConstraint: cn-northwest-1
    objectOwnership: ObjectWriter
    serverSideEncryptionConfiguration:
      rules:
        - applyServerSideEncryptionByDefault:
            sseAlgorithm: AES256
    tagging:
      tagSet:
        - key: owner
          value: konvery-sre
    versioningConfiguration:
      status: Enabled
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
---
apiVersion: s3.aws.crossplane.io/v1beta1
kind: Bucket
metadata:
  name: non-prod-workload-annomaker-artifact
spec:
  deletionPolicy: Delete
  forProvider:
    acl: private
    lifecycleConfiguration:
      rules:
        - abortIncompleteMultipartUpload:
            daysAfterInitiation: 7
          status: Enabled
    locationConstraint: cn-northwest-1
    objectOwnership: ObjectWriter
    serverSideEncryptionConfiguration:
      rules:
        - applyServerSideEncryptionByDefault:
            sseAlgorithm: AES256
    tagging:
      tagSet:
        - key: owner
          value: konvery-sre
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
