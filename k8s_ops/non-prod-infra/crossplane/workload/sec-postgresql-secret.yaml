apiVersion: secretsmanager.aws.crossplane.io/v1beta1
kind: Secret
metadata:
  name: non-prod-workload-postgresql
spec:
  forProvider:
    region: cn-northwest-1
    description: "workload db cluster's secret"
    forceDeleteWithoutRecovery: true
    #recoveryWindowInDays: 7
    stringSecretRef:
      name: non-prod-workload-postgresql
      namespace: crossplane-system
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
