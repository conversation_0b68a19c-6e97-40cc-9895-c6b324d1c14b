apiVersion: secretsmanager.aws.crossplane.io/v1beta1
kind: Secret
metadata:
  name: wildcard-np-konvery-work
spec:
  forProvider:
    region: cn-northwest-1
    description: "non production konvery work certificate"
    forceDeleteWithoutRecovery: true
    #recoveryWindowInDays: 7
    stringSecretRef:
      name: wildcard-np-konvery-work
      namespace: infra
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
