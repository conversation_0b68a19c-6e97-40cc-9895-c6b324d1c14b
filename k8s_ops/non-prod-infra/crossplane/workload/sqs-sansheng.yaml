---
apiVersion: sqs.aws.crossplane.io/v1beta1
kind: Queue
metadata:
  name: non-prod-workload-sqs-sansheng
spec:
  forProvider:
    policy: |
      {
        "Version": "2008-10-17",
        "Statement": [
          {
            "Effect": "Allow",
            "Principal": {
              "AWS": "*"
            },
            "Action": "SQS:SendMessage",
            "Resource": "arn:aws-cn:sqs:cn-northwest-1:035532479701:non-prod-workload-sqs-sansheng",
            "Condition": {
              "ArnLike": {
                "aws:SourceArn": "arn:aws-cn:sns:cn-northwest-1:035532479701:non-prod-workload-sns-sansheng"
              }
            }
          }
        ]
      }
    receiveMessageWaitTimeSeconds: 20
    region: cn-northwest-1
    visibilityTimeout: 3
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
  writeConnectionSecretToRef:
    name: non-prod-workload-sqs-sansheng
    namespace: infra
---
apiVersion: sqs.aws.crossplane.io/v1beta1
kind: Queue
metadata:
  name: non-prod-workload-sqs-sansheng-anno
spec:
  forProvider:
    policy: |
      {
        "Version": "2008-10-17",
        "Statement": [
          {
            "Effect": "Allow",
            "Principal": {
              "AWS": "*"
            },
            "Action": "SQS:SendMessage",
            "Resource": "arn:aws-cn:sqs:cn-northwest-1:035532479701:non-prod-workload-sqs-sansheng-anno",
            "Condition": {
              "ArnLike": {
                "aws:SourceArn": "arn:aws-cn:sns:cn-northwest-1:035532479701:non-prod-workload-sns-sansheng-anno"
              }
            }
          }
        ]
      }
    receiveMessageWaitTimeSeconds: 20
    region: cn-northwest-1
    visibilityTimeout: 3
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
  writeConnectionSecretToRef:
    name: non-prod-workload-sqs-sansheng-anno
    namespace: infra
---
apiVersion: sqs.aws.crossplane.io/v1beta1
kind: Queue
metadata:
  name: non-prod-workload-sqs-sansheng-annostat
spec:
  forProvider:
    policy: |
      {
        "Version": "2008-10-17",
        "Statement": [
          {
            "Effect": "Allow",
            "Principal": {
              "Service": "sns.amazonaws.com"
            },
            "Action": "SQS:SendMessage",
            "Resource": "arn:aws-cn:sqs:cn-northwest-1:035532479701:non-prod-workload-sqs-sansheng-annostat",
            "Condition": {
              "ArnLike": {
                "aws:SourceArn": "arn:aws-cn:sns:cn-northwest-1:035532479701:non-prod-workload-sns-sansheng-anno"
              }
            }
          }
        ]
      }
    receiveMessageWaitTimeSeconds: 20
    region: cn-northwest-1
    visibilityTimeout: 3
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
  writeConnectionSecretToRef:
    name: non-prod-workload-sqs-sansheng-annostat
    namespace: infra
---
apiVersion: sqs.aws.crossplane.io/v1beta1
kind: Queue
metadata:
  name: non-prod-workload-sqs-sansheng-annofeed
spec:
  forProvider:
    policy: |
      {
        "Version": "2008-10-17",
        "Statement": [
          {
            "Effect": "Allow",
            "Principal": {
              "Service": "sns.amazonaws.com"
            },
            "Action": "SQS:SendMessage",
            "Resource": "arn:aws-cn:sqs:cn-northwest-1:035532479701:non-prod-workload-sqs-sansheng-annofeed",
            "Condition": {
              "ArnLike": {
                "aws:SourceArn": "arn:aws-cn:sns:cn-northwest-1:035532479701:non-prod-workload-sns-sansheng-anno"
              }
            }
          }
        ]
      }
    receiveMessageWaitTimeSeconds: 20
    region: cn-northwest-1
    visibilityTimeout: 3
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
  writeConnectionSecretToRef:
    name: non-prod-workload-sqs-sansheng-annofeed
    namespace: infra
