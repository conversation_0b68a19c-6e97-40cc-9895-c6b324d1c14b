---
apiVersion: sns.aws.crossplane.io/v1beta1
kind: Topic
metadata:
  name: non-prod-workload-sns-sansheng-staging
spec:
  forProvider:
    name: non-prod-workload-sns-sansheng-staging
    region: cn-northwest-1
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
  writeConnectionSecretToRef:
    name: non-prod-workload-sns-sansheng-staging
    namespace: infra
# ---
# apiVersion: sns.aws.crossplane.io/v1beta1
# kind: Subscription
# metadata:
#   name: non-prod-workload-sns-sansheng-staging
# spec:
#   forProvider:
#     endpoint: arn:aws-cn:sqs:cn-northwest-1:035532479701:non-prod-workload-sqs-sansheng-staging
#     protocol: sqs
#     rawMessageDelivery: "true"
#     region: cn-northwest-1
#     topicArnRef:
#       name: non-prod-workload-sns-sansheng-staging
#   providerConfigRef:
#     name: non-prod-infra-crossplane-provider-aws-config
---
apiVersion: sns.aws.crossplane.io/v1beta1
kind: Topic
metadata:
  name: non-prod-workload-sns-sansheng-anno-staging
spec:
  forProvider:
    name: non-prod-workload-sns-sansheng-anno-staging
    region: cn-northwest-1
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
  writeConnectionSecretToRef:
    name: non-prod-workload-sns-sansheng-anno-staging
    namespace: infra
# ---
# apiVersion: sns.aws.crossplane.io/v1beta1
# kind: Subscription
# metadata:
#   name: non-prod-workload-sns-sansheng-anno-staging
# spec:
#   forProvider:
#     endpoint: arn:aws-cn:sqs:cn-northwest-1:035532479701:non-prod-workload-sqs-sansheng-anno-staging
#     protocol: sqs
#     rawMessageDelivery: "true"
#     region: cn-northwest-1
#     topicArnRef:
#       name: non-prod-workload-sns-sansheng-anno-staging
#   providerConfigRef:
#     name: non-prod-infra-crossplane-provider-aws-config
---
apiVersion: sns.aws.crossplane.io/v1beta1
kind: Subscription
metadata:
  name: non-prod-workload-sns-sansheng-annostat-staging
spec:
  forProvider:
    endpoint: arn:aws-cn:sqs:cn-northwest-1:035532479701:non-prod-workload-sqs-sansheng-annostat-staging
    protocol: sqs
    rawMessageDelivery: "true"
    region: cn-northwest-1
    topicArnRef:
      name: non-prod-workload-sns-sansheng-anno-staging
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
---
apiVersion: sns.aws.crossplane.io/v1beta1
kind: Subscription
metadata:
  name: non-prod-workload-sns-sansheng-annofeed-staging
spec:
  forProvider:
    endpoint: arn:aws-cn:sqs:cn-northwest-1:035532479701:non-prod-workload-sqs-sansheng-annofeed-staging
    protocol: sqs
    rawMessageDelivery: "true"
    region: cn-northwest-1
    topicArnRef:
      name: non-prod-workload-sns-sansheng-anno-staging
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
