---
apiVersion: sqs.aws.crossplane.io/v1beta1
kind: Queue
metadata:
  name: non-prod-workload-sqs-sansheng-staging
spec:
  forProvider:
    policy: |
      {
        "Version": "2008-10-17",
        "Statement": [
          {
            "Effect": "Allow",
            "Principal": {
              "AWS": "*"
            },
            "Action": "SQS:SendMessage",
            "Resource": "arn:aws-cn:sqs:cn-northwest-1:035532479701:non-prod-workload-sqs-sansheng-staging",
            "Condition": {
              "ArnLike": {
                "aws:SourceArn": "arn:aws-cn:sns:cn-northwest-1:035532479701:non-prod-workload-sns-sansheng-staging"
              }
            }
          }
        ]
      }
    receiveMessageWaitTimeSeconds: 20
    region: cn-northwest-1
    visibilityTimeout: 3
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
  writeConnectionSecretToRef:
    name: non-prod-workload-sqs-sansheng-staging
    namespace: infra
---
apiVersion: sqs.aws.crossplane.io/v1beta1
kind: Queue
metadata:
  name: non-prod-workload-sqs-sansheng-anno-staging
spec:
  forProvider:
    policy: |
      {
        "Version": "2008-10-17",
        "Statement": [
          {
            "Effect": "Allow",
            "Principal": {
              "AWS": "*"
            },
            "Action": "SQS:SendMessage",
            "Resource": "arn:aws-cn:sqs:cn-northwest-1:035532479701:non-prod-workload-sqs-sansheng-anno-staging",
            "Condition": {
              "ArnLike": {
                "aws:SourceArn": "arn:aws-cn:sns:cn-northwest-1:035532479701:non-prod-workload-sns-sansheng-anno-staging"
              }
            }
          }
        ]
      }
    receiveMessageWaitTimeSeconds: 20
    region: cn-northwest-1
    visibilityTimeout: 3
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
  writeConnectionSecretToRef:
    name: non-prod-workload-sqs-sansheng-anno-staging
    namespace: infra
---
apiVersion: sqs.aws.crossplane.io/v1beta1
kind: Queue
metadata:
  name: non-prod-workload-sqs-sansheng-annostat-staging
spec:
  forProvider:
    policy: |
      {
        "Version": "2008-10-17",
        "Statement": [
          {
            "Effect": "Allow",
            "Principal": {
              "Service": "sns.amazonaws.com"
            },
            "Action": "SQS:SendMessage",
            "Resource": "arn:aws-cn:sqs:cn-northwest-1:035532479701:non-prod-workload-sqs-sansheng-annostat-staging",
            "Condition": {
              "ArnLike": {
                "aws:SourceArn": "arn:aws-cn:sns:cn-northwest-1:035532479701:non-prod-workload-sns-sansheng-anno-staging"
              }
            }
          }
        ]
      }
    receiveMessageWaitTimeSeconds: 20
    region: cn-northwest-1
    visibilityTimeout: 3
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
  writeConnectionSecretToRef:
    name: non-prod-workload-sqs-sansheng-annostat-staging
    namespace: infra
---
apiVersion: sqs.aws.crossplane.io/v1beta1
kind: Queue
metadata:
  name: non-prod-workload-sqs-sansheng-annofeed-staging
spec:
  forProvider:
    policy: |
      {
        "Version": "2008-10-17",
        "Statement": [
          {
            "Effect": "Allow",
            "Principal": {
              "Service": "sns.amazonaws.com"
            },
            "Action": "SQS:SendMessage",
            "Resource": "arn:aws-cn:sqs:cn-northwest-1:035532479701:non-prod-workload-sqs-sansheng-annofeed-staging",
            "Condition": {
              "ArnLike": {
                "aws:SourceArn": "arn:aws-cn:sns:cn-northwest-1:035532479701:non-prod-workload-sns-sansheng-anno-staging"
              }
            }
          }
        ]
      }
    receiveMessageWaitTimeSeconds: 20
    region: cn-northwest-1
    visibilityTimeout: 3
  providerConfigRef:
    name: non-prod-infra-crossplane-provider-aws-config
  writeConnectionSecretToRef:
    name: non-prod-workload-sqs-sansheng-annofeed-staging
    namespace: infra
