---
apiVersion: keycloak.org/v1alpha1
kind: KeycloakClient
metadata:
  name: artifactory
  namespace: infra
  labels:
    app: keycloak
spec:
  client:
    clientId: artifactory
    name: artifactory
    protocol: openid-connect
    standardFlowEnabled: true
    directAccessGrantsEnabled: true
    defaultClientScopes:
      - "email"
      - "profile"
      - "roles"
      - "web-origins"
    optionalClientScopes:
      - "address"
      - "microprofile-jwt"
      - "offline_access"
      - "phone"
    adminUrl: https://artifactory.np.konvery.work/
    webOrigins:
      - "https://artifactory.np.konvery.work"
    rootUrl: https://artifactory.np.konvery.work/
    redirectUris:
      - "https://artifactory.np.konvery.work/*"
  realmSelector:
    matchLabels:
      app: keycloak
