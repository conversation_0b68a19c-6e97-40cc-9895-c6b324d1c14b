---
apiVersion: keycloak.org/v1alpha1
kind: KeycloakClient
metadata:
  name: gitlab
  namespace: infra
  labels:
    app: keycloak
spec:
  client:
    clientId: gitlab
    name: gitlab
    protocol: openid-connect
    standardFlowEnabled: true
    directAccessGrantsEnabled: true
    defaultClientScopes:
      - "email"
      - "profile"
      - "roles"
      - "web-origins"
    optionalClientScopes:
      - "address"
      - "microprofile-jwt"
      - "offline_access"
      - "phone"
    adminUrl: https://gitlab.np.konvery.work/
    webOrigins:
      - "https://gitlab.np.konvery.work"
    rootUrl: https://gitlab.np.konvery.work/
    redirectUris:
      - "https://gitlab.np.konvery.work/*"
  realmSelector:
    matchLabels:
      app: keycloak
