---
apiVersion: keycloak.org/v1alpha1
kind: KeycloakClient
metadata:
  name: grafana
  namespace: infra
  labels:
    app: keycloak
spec:
  client:
    clientId: grafana
    name: grafana
    protocol: openid-connect
    standardFlowEnabled: true
    directAccessGrantsEnabled: true
    defaultClientScopes:
      - "email"
      - "profile"
      - "roles"
      - "web-origins"
      - "groups"
    optionalClientScopes:
      - "address"
      - "microprofile-jwt"
      - "offline_access"
      - "phone"
    adminUrl: https://grafana.np.konvery.work/
    webOrigins:
      - "https://grafana.np.konvery.work"
    rootUrl: https://grafana.np.konvery.work/
    redirectUris:
      - "https://grafana.np.konvery.work/login/*"
    # baseUrl: /applications
  realmSelector:
    matchLabels:
      app: keycloak
