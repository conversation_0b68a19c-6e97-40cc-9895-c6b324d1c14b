---
apiVersion: k8s.keycloak.org/v2alpha1
kind: Keycloak
metadata:
  labels:
    app: keycloak
  name: keycloak
  namespace: infra
spec:
  db:
    vendor: postgres
    usernameSecret:
      name: keycloak-db-secret
      key: POSTGRES_USERNAME
    passwordSecret:
      name: keycloak-db-secret
      key: POSTGRES_PASSWORD
    host: non-prod-infra-postgresql.cluster-ck7i7eqx0frz.rds.cn-northwest-1.amazonaws.com.cn
    database: keycloak
    port: 5432
  instances: 1
  http:
    httpEnabled: false
    # httpsPort: 8443
    tlsSecret: wildcard-np-konvery-work
  hostname:
    # hostname: keycloak.np.konvery.work
    strict: false
    strictBackchannel: false
  ingress:
    enabled: false
  transaction:
    xaEnabled: false
  
  # keycloakDeploymentSpec:
  #   experimental:
  #     affinity:
  #       nodeAffinity:
  #         requiredDuringSchedulingIgnoredDuringExecution:
  #           nodeSelectorTerms:
  #             - matchExpressions:
  #               - key: eks.amazonaws.com/capacityType
  #                 operator: In
  #                 values:
  #                 - ON_DEMAND
