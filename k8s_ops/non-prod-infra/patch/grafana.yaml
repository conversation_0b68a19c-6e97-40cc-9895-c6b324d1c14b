stringData:
  grafana.ini: |
    [server]
    root_url = https://grafana.np.konvery.work
    [auth.generic_oauth]
    name = Keycloak
    enabled = true
    allow_sign_up = true
    client_id = grafana
    client_secret = 81cwkm4m2zb10KPzHEsFMjnNyzwaLRgw
    scopes = openid roles email profile web-origins
    allow_assign_grafana_admin = true
    auth_url = https://keycloak.np.konvery.work/realms/konvery/protocol/openid-connect/auth
    token_url = https://keycloak.np.konvery.work/realms/konvery/protocol/openid-connect/token
    api_url = https://keycloak.np.konvery.work/realms/konvery/protocol/openid-connect/userinfo
    role_attribute_path = "contains(realm_access.roles[*], 'kvy-admin') && 'Admin' || contains(realm_access.roles[*], 'kvy-editor') && 'Editor' || 'Viewer'"
    [date_formats]
    default_timezone = Asia/Shanghai
    [security]
    admin_password = c3CSE2n62i3tywKX
    [users]
    auto_assign_org = true
