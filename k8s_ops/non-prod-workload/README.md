# Non production workload cluster setup

## Preparing Environment

```shell
aws eks describe-cluster --name non-prod-workload-cluster --query "cluster.identity.oidc.issuer" --output text
https://oidc.eks.cn-northwest-1.amazonaws.com.cn/id/800D32D9C2C460A1298FAAD150156907
```

```shell
aws eks update-kubeconfig --region cn-northwest-1 --name non-prod-workload-cluster
```

### Update cluster role, user, and user group

```shell
# Update cluster role
kubectl apply -f k8s_ops/misc/cluster-role/kvy-clusterrole.yaml
# Cluster role bindings
kubectl apply -f k8s_ops/misc/cluster-role/cluster-role-binding.yaml

# Patch aws-auth to map users, user groups, and roles
kubectl -n kube-system patch configmap aws-auth --patch-file k8s_ops/non-prod-workload/patch/aws-auth.yaml
```

## Infra perspective: provisioning domain, database and role for workload

### chang to infra cluster context for crossplane provider-aws ops

### r53 for np.api.konvery.com

```shell
kaf k8s_ops/non-prod-infra/crossplane/workload/r53-zone-api-konvery-com-public.yaml
kaf k8s_ops/non-prod-infra/crossplane/workload/r53-zone-np-api-konvery-com-public.yaml
```

```shell
# assosicate workload vpc to private zone for argocd internal access
aws route53 associate-vpc-with-hosted-zone --hosted-zone-id Z00610953OLRDDTORE8UR --vpc VPCRegion=cn-northwest-1,VPCId=vpc-0c6868740b8ec800a
```

### cert for np-konvery-work

```shell
kaf k8s_ops/non-prod-infra/crossplane/workload/sec-wildcard-np-konvery-work.yaml
```

### database

```shell
# database security group
kaf k8s_ops/non-prod-infra/crossplane/workload/ec2-sg-postgresql.yaml

# manually init db passwd
## To be changed by community, PR raised
kubectl create secret generic manual-initial-db-pw-workload
--from-literal=password=<passwd> -n crossplane-system

# install rds for all infra shared DB Cluster
kaf k8s_ops/non-prod-infra/crossplane/workload/rds-postgresql-dbcluster.yaml

# install rds instance as rw node in DB Cluster
kaf k8s_ops/non-prod-infra/crossplane/workload/rds-postgresql-dbcluster.yaml

# push rds secrets to aws secret manager
kaf k8s_ops/non-prod-infra/crossplane/workload/sec-postgresql-secret.yaml
```

### role for middleware

Only need to bind corresponding policy to roles created

```shell
# cert-manager role
kaf k8s_ops/non-prod-infra/crossplane/workload/iam-role-cert-manager.yaml

# external-dns role
kaf k8s_ops/non-prod-infra/crossplane/workload/iam-role-external-dns.yaml

# external-secret role
kaf k8s_ops/non-prod-infra/crossplane/workload/iam-role-external-secret.yaml
```

## Workload perspective: setup & provisioning middleware

### priority class

```shell
kaf k8s_ops/non-prod-workload/crds/priorityclass.yaml
```

### Metrics server

```shell
# https://docs.aws.amazon.com/eks/latest/userguide/metrics-server.html
# kubectl apply -f https://github.com/kubernetes-sigs/metrics-server/releases/latest/download/components.yaml
kaf k8s_ops/misc/metrics-server.yaml

kubectl -n kube-system patch deployment metrics-server --patch-file k8s_ops/non-prod-workload/patch/metrics-server.yaml
```

### aws-ebs-csi-driver

``` shell
# !!! infra context
kaf k8s_ops/non-prod-infra/crossplane/workload/iam-role-aws-ebs-csi-driver.yaml

# using aws managed ebs csi driver
kaf k8s_ops/non-prod-workload/crds/storageclass.yaml

# !NOTE: restart pod ebs-csi-controller-* to make env "AWS_ROLE_ARN" etc injected to pod

# make ebs-sc the only default driver
kubectl patch storageclass gp2 -p '{"metadata": {"annotations":{"storageclass.kubernetes.io/is-default-class":"false"}}}'
```

### Install ArgoCD

```shell
kubectl create namespace argocd
# kubectl apply -n argocd -f https://raw.githubusercontent.com/argoproj/argo-cd/stable/manifests/install.yaml

wget -e use_proxy=yes -e https_proxy=127.0.0.1:7890 https://raw.githubusercontent.com/argoproj/argo-cd/v2.4.0/manifests/install.yaml
kubectl apply -n argocd -f install.yaml

kaf k8s_ops/non-prod-workload/argocd/appproj-infra.yaml
kaf k8s_ops/non-prod-workload/crds/configmap-argocd.yaml

# Patch node to be scheduled on ON_DEMAND node
kubectl -n argocd patch deployment argocd-applicationset-controller --patch-file k8s_ops/non-prod-workload/patch/argocd.yaml
kubectl -n argocd patch deployment argocd-dex-server --patch-file k8s_ops/non-prod-workload/patch/argocd-dex.yaml
kubectl -n argocd patch deployment argocd-notifications-controller --patch-file k8s_ops/non-prod-workload/patch/argocd.yaml
kubectl -n argocd patch deployment argocd-redis --patch-file k8s_ops/non-prod-workload/patch/argocd.yaml
kubectl -n argocd patch deployment argocd-repo-server --patch-file k8s_ops/non-prod-workload/patch/argocd.yaml
kubectl -n argocd patch deployment argocd-server --patch-file k8s_ops/non-prod-workload/patch/argocd.yaml

# Retrieve passwords
kubectl get secret -n argocd argocd-initial-admin-secret -o json | jq ".data.password | @base64d"
```

#### ArgoCD upgrade

```shell
# upgradeing argocd
kubectl apply -n argocd -f https://raw.githubusercontent.com/argoproj/argo-cd/stable/manifests/install.yaml
# updating dex file and patch
kubectl -n argocd patch deployment argocd-dex-server --patch-file k8s_ops/non-prod-workload/patch/argocd-dex.yaml
# Configure argocd oidc
kaf k8s_ops/non-prod-workload/crds/configmap-argocd.yaml
```


### Install crossplane

Note: we only enable db operations for workload cluster

```shell
kaf k8s_ops/non-prod-workload/argocd/crossplane.yaml
kaf k8s_ops/non-prod-workload/crossplane/provider/provider-postgresql.yaml
```

### Install cert-manager

```shell
kaf argocd/cert-manager.yaml

kaf crds/cert-clusterissuer-np-api-konvery-com.yaml
kaf crds/cert-wildcard-np-api-konvery-com.yaml
```

### Install external-secret

```shell
kaf k8s_ops/non-prod-workload/argocd/external-secret.yaml

# retrieve rds role secret
kaf k8s_ops/non-prod-workload/crds/extsec-postgresql-role.yaml
```

### Install external-dns

```shell
kaf k8s_ops/non-prod-workload/argocd/external-dns-internal.yaml
```

### Install ingress-nginx

```shell
kaf k8s_ops/non-prod-workload/argocd/ingress-nginx-internal.yaml
kaf k8s_ops/non-prod-workload/argocd/ingress-nginx-external.yaml
```

### Configure argocd

```shell
# retrieve certificate
kaf k8s_ops/non-prod-workload/crds/extsec-wildcard-np-konvery-work.yaml
kaf k8s_ops/non-prod-workload/crds/ingress-argocd-workload.yaml
```

## Application deployment

### apisix

```shell
kaf k8s_ops/non-prod-workload/argocd/etcd.yaml
kaf k8s_ops/non-prod-workload/argocd/apisix.yaml
kaf k8s_ops/non-prod-workload/crds/ingress-apisix-dashboard.yaml
```

### temporal

```shell
kaf k8s_ops/prod-workload/crossplane/postgresql-role-temporal.yaml
kaf k8s_ops/prod-workload/crossplane/postgresql-config-temporal.yaml
# Be careful with crossplane annotations
kaf k8s_ops/prod-workload/crossplane/postgresql-db-temporal.yaml

# launch alpine
# apk add git go
# git clone https://github.com/temporalio/temporal
# cd  temporal/tools
# go env -w GOPROXY=https://goproxy.cn
# go build
# export PG_HOST=non-prod-workload-postgresql.cluster-ck7i7eqx0frz.rds.cn-northwest-1.amazonaws.com.cn
# export TEMPORAL_DB=temporal
# export VISIBILITY_DB=temporal_visibility
# export PGPASSWORD=2CWbUyX1zy5NqG7FWlee6WA2gar

install-schema-postgresql: temporal-sql-tool
	@printf $(COLOR) "Install Postgres schema..."
	./temporal-sql-tool -ep ${PG_HOST} -u temporal -p 5432 --pl postgres --db ${TEMPORAL_DB} drop -f
	./temporal-sql-tool -ep ${PG_HOST} -u temporal -p 5432 --pl postgres --db ${TEMPORAL_DB} create
	./temporal-sql-tool -ep ${PG_HOST} -u temporal -p 5432 --pl postgres --db ${TEMPORAL_DB} setup -v 0.0
	./temporal-sql-tool -ep ${PG_HOST} -u temporal -p 5432 --pl postgres --db ${TEMPORAL_DB} update-schema -d ./schema/postgresql/v96/temporal/versioned
	./temporal-sql-tool -ep ${PG_HOST} -u temporal -p 5432 --pl postgres --db ${VISIBILITY_DB} drop -f
	./temporal-sql-tool -ep ${PG_HOST} -u temporal -p 5432 --pl postgres --db ${VISIBILITY_DB} create
	./temporal-sql-tool -ep ${PG_HOST} -u temporal -p 5432 --pl postgres --db ${VISIBILITY_DB} setup-schema -v 0.0
	./temporal-sql-tool -ep ${PG_HOST} -u temporal -p 5432 --pl postgres --db ${VISIBILITY_DB} update-schema -d ./schema/postgresql/v96/visibility/versioned

kaf k8s_ops/prod-workload/argocd/temporal.yaml
kaf k8s_ops/prod-workload/crds/ingress-temporal.yaml

# upgrade temporal schema
# launch alpine
kaf k8s_ops/misc/alpine.yaml
sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
apk add git go make
git clone https://gitlab.rp.konvery.work/infra/temporal.git
go env -w GOPROXY=https://goproxy.cn
make temporal-sql-tool
export SQL_USER=temporal
export SQL_PASSWORD=2CWbUyX1zy5NqG7FWlee6WA2gar
export SQL_HOST=non-prod-workload-postgresql.cluster-ck7i7eqx0frz.rds.cn-northwest-1.amazonaws.com.cn
export TEMPORAL_DB=temporal
export VISIBILITY_DB=temporal_visibility
./temporal-sql-tool -ep ${SQL_HOST} -u ${SQL_USER} -p 5432 --pl postgres --db ${TEMPORAL_DB} setup -v 0.0
./temporal-sql-tool -ep ${SQL_HOST} -u ${SQL_USER} -p 5432 --pl postgres --db ${VISIBILITY_DB} setup -v 0.0
./temporal-sql-tool -u $SQL_USER --pw $SQL_PASSWORD -p 5432 --pl postgres --db $TEMPORAL_DB --ep $SQL_HOST update-schema  -d ./schema/postgresql/v12/temporal/versioned
./temporal-sql-tool -u $SQL_USER --pw $SQL_PASSWORD -p 5432 --pl postgres --db $VISIBILITY_DB --ep $SQL_HOST update-schema update-schema -d ./schema/postgresql/v12/visibility/versioned
```

### sentry

```shell
# s3 bucket, iam role and policy
# non-prod-infra context
kaf k8s_ops/non-prod-infra/crossplane/workload/s3-sentry.yaml
kaf k8s_ops/non-prod-infra/crossplane/workload/iam-role-sentry.yaml
kaf k8s_ops/non-prod-infra/crossplane/workload/iam-policy-sentry.yaml

# db and other peripheral components
# non-prod-workload context
kaf k8s_ops/non-prod-workload/crossplane/postgresql-role-sentry.yaml
kaf k8s_ops/non-prod-workload/crossplane/postgresql-config-sentry.yaml
kaf k8s_ops/non-prod-workload/crossplane/postgresql-db-sentry.yaml

# sentry deployment
kaf k8s_ops/prod-workload/argocd/sentry.yaml

# Hack topics to avoid sentry-ingest-replay-recordings continousely crash
# Login to kafka-0
/opt/bitnami/kafka/bin/kafka-topics.sh --create --topic ingest-replay-recordings --bootstrap-server localhost:9092
```

### sansheng

* create app project

```shell
kaf k8s_ops/non-prod-workload/crds/argocd-appproj-sansheng.yaml
```

* create k8s namespace

```shell
k create namespace sansheng
```

* create service account for s3 access

```shell
# infra context
kaf k8s_ops/non-prod-infra/crossplane/workload/s3-sansheng.yaml
kaf k8s_ops/non-prod-infra/crossplane/workload/iam-policy-sansheng.yaml
kaf k8s_ops/non-prod-infra/crossplane/workload/iam-role-sansheng.yaml
```

* create postgresql database

```shell
# workload context
kaf k8s_ops/non-prod-workload/crossplane/postgresql-role-sansheng.yaml
kaf k8s_ops/non-prod-workload/crossplane/postgresql-config-sansheng.yaml
kaf k8s_ops/non-prod-workload/crossplane/postgresql-db-sansheng.yaml
```

* create secret for artifactory token

```shell
# workload context
kaf k8s_ops/non-prod-workload/crds/secret-sansheng.yaml
```

* create ingress for services

```shell
kaf k8s_ops/non-prod-workload/crds/ingress-apisix-label.yaml
kaf k8s_ops/non-prod-workload/crds/ingress-apisix-anno.yaml
```

### Install Prometheus

Using deployment of prometheus operator

```shell
# upgrade begin
# clone repo for k8s v1.26
git clone https://github.com/prometheus-operator/kube-prometheus
git checkout release-0.13

# Create the namespace and CRDs, and then wait for them to be available before creating the remaining resources
# Note that due to some CRD size we are using kubectl server-side apply feature which is generally available since kubernetes 1.22.
# If you are using previous kubernetes versions this feature may not be available and you would need to use kubectl create instead.
kubectl apply --server-side -f manifests/setup
kubectl --namespace=monitoring wait --for condition=Established --all CustomResourceDefinition
kubectl apply -f manifests/

# affinity context begin
kubectl -n monitoring patch alertmanager.monitoring.coreos.com/main --type merge --patch-file k8s_ops/non-prod-workload/patch/prometheus-alertmanager.yaml
## + persistentVolume
kubectl -n monitoring patch prometheus.monitoring.coreos.com/k8s --type merge --patch-file k8s_ops/non-prod-workload/patch/prometheus-k8s.yaml
kubectl -n monitoring patch deployment blackbox-exporter --patch-file k8s_ops/non-prod-workload/patch/prometheus-blackbox-exporter.yaml
kubectl -n monitoring patch deployment grafana --patch-file k8s_ops/non-prod-workload/patch/prometheus-grafana.yaml
## + container patch
kubectl -n monitoring patch deployment kube-state-metrics --patch-file k8s_ops/non-prod-workload/patch/prometheus-kube-state-metrics.yaml
## + container patch
kubectl -n monitoring patch deployment prometheus-adapter --patch-file k8s_ops/non-prod-workload/patch/prometheus-adapter.yaml
kubectl -n monitoring patch deployment prometheus-operator --patch-file k8s_ops/non-prod-workload/patch/prometheus-operator.yaml
# affinity context end

# patch role permissions
kubectl patch clusterrole/prometheus-k8s --patch-file k8s_ops/non-prod-workload/patch/prometheus-k8s-cluster-role.yaml

# delete all network policies
for i in $(kubectl -n monitoring get networkpolicy -o jsonpath='{.items[*].metadata.name}')
do
   kubectl -n monitoring delete networkpolicy $i
done
# upgrade end

# add ingress
kaf k8s_ops/non-prod-workload/crds/ingress-prometheus-grafana.yaml

# add oidc for keycloak
kubectl -n monitoring patch secret grafana-config --patch-file k8s_ops/non-prod-workload/patch/grafana.yaml
```

### Install Loki

```shell
# create pull secret
# refer to k8s_ops/non-prod-workload/crds/secret-artifactory-token.yaml

# create app
kaf k8s_ops/non-prod-workload/argocd/loki.yaml
```

### keto

```shell
# postgresql db
kaf k8s_ops/non-prod-workload/crossplane/postgresql-role-keto.yaml
kaf k8s_ops/non-prod-workload/crossplane/postgresql-config-keto.yaml
kaf k8s_ops/non-prod-workload/crossplane/postgresql-db-keto.yaml

# extsec
kaf k8s_ops/non-prod-workload/argocd/external-secret.yaml
kaf k8s_ops/non-prod-workload/crds/extsec-keto.yaml

# app
kaf k8s_ops/non-prod-workload/argocd/keto.yaml
```

### Update cluster role, user, and user group

```shell
# Update cluster role
kaf k8s_ops/misc/cluster-role/non-prod/eks-console-dashboard-full-access-clusterrole.yaml

# Edit aws-auth to map users, user groups, and roles
k -n kube-system edit configmap/aws-auth

# Add the following blocks to aws-auth (alter the user name as needed)
  mapRoles: |
    - groups:
      - system:bootstrappers
      - system:nodes
      rolearn: arn:aws-cn:iam::035532479701:role/karpenter-eks-node-group-20220629015507214300000001
      username: system:node:{{EC2PrivateDNSName}}
  mapUsers: |
    - userarn: arn:aws-cn:iam::035532479701:group/KonveryEngineer
      username: konveryengineer
      groups:
        - eks-console-dashboard-full-access-group
    - userarn: arn:aws-cn:iam::035532479701:user/baoshi
      username: baoshi
      groups:
        - eks-console-dashboard-full-access-group
```

### patch clusterrole

kubectl patch clusterrole/prometheus-k8s --patch-file k8s_ops/non-prod-workload/patch/rbac-clusterroles.yaml
