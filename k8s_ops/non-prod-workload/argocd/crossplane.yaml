apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: crossplane
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: infra
  destination:
    namespace: crossplane-system
    server: "https://kubernetes.default.svc"
  source:
    repoURL: "https://charts.crossplane.io/stable"
    targetRevision: "1.12.2"
    chart: crossplane
    helm:
      values: |
        affinity:
          nodeAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
                - matchExpressions:
                  - key: eks.amazonaws.com/capacityType
                    operator: In
                    values:
                    - ON_DEMAND
        image:
          repository: docker.m.daocloud.io/crossplane/crossplane
  syncPolicy:
    automated:
      selfHeal: true
      prune: true
    syncOptions:
      - CreateNamespace=true
  ignoreDifferences:
    - kind: ClusterRole
      jsonPointers:
        - /rules
