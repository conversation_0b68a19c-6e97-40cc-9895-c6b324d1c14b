apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: external-secrets
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: infra
  destination:
    namespace: infra
    server: 'https://kubernetes.default.svc'
  source:
    repoURL: 'https://charts.external-secrets.io'
    targetRevision: '0.5.9'
    chart: external-secrets
    helm:
      values: |
        image:
          repository: ghcr.m.daocloud.io/external-secrets/external-secrets
        webhook:
          image:
            repository: ghcr.m.daocloud.io/external-secrets/external-secrets
        certController:
          image:
            repository: ghcr.m.daocloud.io/external-secrets/external-secrets
        leaderElect: true
        replicaCount: 3
        serviceAccount:
          annotations:
            eks.amazonaws.com/role-arn: arn:aws-cn:iam::************:role/non-prod-workload-externalsecret
  syncPolicy:
    automated:
      selfHeal: true
      prune: true
    syncOptions:
    - CreateNamespace=true
---
apiVersion: external-secrets.io/v1beta1
kind: ClusterSecretStore
metadata:
  name: external-secrets
spec:
  provider:
    aws:
      service: SecretsManager
      region: cn-northwest-1
---
# https://external-secrets.io/v0.5.9/provider-kubernetes/
# NOTE: SelfSubjectRulesReview permission is required in order to validation work properly. 
# Manually hacked
apiVersion: external-secrets.io/v1beta1
kind: ClusterSecretStore
metadata:
  name: external-secrets-kubernetes
spec:
  provider:
    kubernetes:
      auth:
        serviceAccount:
          name: external-secrets
          namespace: infra
      remoteNamespace: sansheng
      server:
        caProvider:
          type: ConfigMap
          name: kube-root-ca.crt
          key: ca.crt
          namespace: infra
---
apiVersion: external-secrets.io/v1beta1
kind: ClusterSecretStore
metadata:
  name: staging-external-secrets-kubernetes
spec:
  provider:
    kubernetes:
      auth:
        serviceAccount:
          name: external-secrets
          namespace: infra
      remoteNamespace: staging-sansheng
      server:
        caProvider:
          type: ConfigMap
          name: kube-root-ca.crt
          key: ca.crt
          namespace: infra
