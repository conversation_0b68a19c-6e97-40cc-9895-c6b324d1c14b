---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: ingress-nginx-internal
  namespace: argocd
spec:
  destination:
    namespace: infra
    server: https://kubernetes.default.svc
  project: infra
  source:
    chart: ingress-nginx
    helm:
      values: |
        affinity:
          nodeAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
                - matchExpressions:
                    - key: eks.amazonaws.com/capacityType
                      operator: In
                      values:
                        - ON_DEMAND
        controller:
          admissionWebhooks:
            patch:
              image:
                registry: k8s.m.daocloud.io
          annotations:
            external-dns-annotation: ingress-nginx-internal
          config:
            proxy-buffer-size: 32k
          extraArgs:
            ingress-class: ingress-nginx-internal
          image:
            pullPolicy: Always
            registry: k8s.m.daocloud.io
          ingressClassByName: true
          ingressClassResource:
            controllerValue: k8s.io/ingress-nginx
            default: true
            enabled: true
            name: ingress-nginx-internal
          livenessProbe:
            failureThreshold: 2
          metrics:
            enabled: true
          podAnnotations:
            prometheus.io/port: 10254
            prometheus.io/scrape: true
          publishService:
            enabled: true
          readinessProbe:
            failureThreshold: 2
            successThreshold: 2
          replicaCount: 3
          scope:
            namespace: default
          service:
            annotations:
              external-dns-annotation: ingress-nginx-internal
              service.beta.kubernetes.io/aws-load-balancer-name: non-prod-workload-internal
              service.beta.kubernetes.io/aws-load-balancer-nlb-target-type: ip
              service.beta.kubernetes.io/aws-load-balancer-scheme: internal
              service.beta.kubernetes.io/aws-load-balancer-type: external
            externalTrafficPolicy: Local
            # ssl-redirect: "true"
          watchIngressWithoutClass: true
        defaultBackend:
            # nodePorts:
            #   http: 32080
            #   https: 32443
          image:
            registry: k8s.m.daocloud.io
          replicaCount: 3
        rbac:
          create: true
    repoURL: https://kubernetes.github.io/ingress-nginx
    targetRevision: 4.7.2
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true