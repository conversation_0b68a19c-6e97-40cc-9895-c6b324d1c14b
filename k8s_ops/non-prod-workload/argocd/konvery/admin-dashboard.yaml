---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: admin-dashboard
  namespace: argocd
spec:
  destination:
    namespace: sansheng
    server: https://kubernetes.default.svc
  project: sansheng
  source:
    chart: admin-dashboard
    helm:
      values: |
        envVars:
          NGINX_ROOT: "/usr/share/nginx/html/nonprod"
          NGINX_ENVSUBST_OUTPUT_DIR: "/etc/nginx/konvery.conf.d"
    repoURL: https://artifactory.rp.konvery.work/artifactory/helm
    targetRevision: 0.*.*
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
