---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: anyconn
  namespace: argocd
spec:
  destination:
    namespace: sansheng
    server: https://kubernetes.default.svc
  project: sansheng
  source:
    chart: sansheng-unicorn
    helm:
      parameters:
        - name: database.DATABASE
          value: anyconn
        - name: service.httpPort
          value: '8050'
        - name: service.grpcPort
          value: '8051'
        - name: envVars.rpc\.services\.iam\.addr
          value: 'iam:8001'
        - name: envVars.temporal\.addr
          value: 'temporal-frontend:7233'
        - name: envVars.custsys\.customers\.dazhuo\.services\.filemanager\.endpoint
          value: >-
            https://zdrive-bigdata.mychery.com/operation/operationManager/token/auth
        - name: envVars.custsys\.customers\.dazhuo\.services\.filemanager\.access_key
          value: KonveryAnnotation
        - name: envVars.custsys\.customers\.dazhuo\.services\.filemanager\.secret_key
          value: K0xKlB06
        - name: envVars.custsys\.customers\.dazhuo\.services\.filemanager\.concurrency
          value: '10'
        - name: >-
            envVars.custsys\.customers\.dazhuo\.services\.filemanager\.extra\.check_url
          value: >-
            https://zdrive-bigdata.mychery.com/manager/dataManager/task/send/label/labelingValidation
        - name: envVars.server\.http\.cors_origins
          value: null,https://admin.np.konvery.work,https://label.np.konvery.work,https://tars.np.konvery.work
        - name: persistence.enabled
          value: 'true'
        - name: persistence.size
          value: 10Gi
        - name: envVars.file_server\.cloudfront\.enabled
          value: 'true'
        - name: envVars.DAZHUO_SKIP_TLS_VERIFY
          value: 'true'
        - name: envVars.DAZHUO_NOTIFY_ANNOS
          value: 'false'
        - name: envVars.BOSS_TIMESTAMP_PAST
          value: '30758400'
        - name: envVars.ANYCONN_WHOOKEVT_NO_AUTO_COMMENT
          value: 'true'
        - name: envVars.ANYCONN_WHOOKEVT_AUTO_REJECT_JOB
          value: 'false'
        - name: autoscaling.minReplicas
          value: '1'
        - name: autoscaling.maxReplicas
          value: '1'
        - name: envVars.otel\.log\.level-
          value: debug
        - name: fullnameOverride
          value: anyconn
        - name: nameOverride
          value: anyconn
        - name: envVars.custsys\.customers\.baidu\.services\.default\.access_key
          value: ALTAKzwokjOoXBJbYJr7Yi2XOi
        - name: envVars.custsys\.customers\.baidu\.services\.default\.secret_key
          value: 43b40417ef39410886adf963ede6abdb
      values: |
        image:
          svcCommand: ["/app/unicorn", "--conf", "/data/conf/anyconn.yaml", "anyconn", "serve"]
          migrateCommand: ["/app/unicorn", "--conf", "/data/conf/anyconn.yaml", "anyconn", "migrate", "autoup"]
        envVars.mq\.producer\.provider: "unspecified"
        envVars.mq\.consumer\.provider: "unspecified"
        serviceAccount:
          create: false
          name: sansheng-annofeed # sansheng-anyconn
          annotations:
            eks.amazonaws.com/role-arn: arn:aws-cn:iam::************:role/prod-workload-sansheng
        hostName: anyconn.np.konvery.work
        ingress:
          enabled: true
          className: "ingress-nginx-internal"
          annotations:
            # nginx.ingress.kubernetes.io/ssl-passthrough: "true"
            external-dns-annotation: ingress-nginx-internal
            nginx.ingress.kubernetes.io/proxy-body-size: 1024m
            nginx.ingress.kubernetes.io/enable-cors: "false"
          hosts:
            - host: anyconn.np.konvery.work
              paths:
                - path: /
                  pathType: ImplementationSpecific
          tls:
            - hosts:
              - anyconn.np.konvery.work
              secretName: wildcard-np-konvery-work
    repoURL: https://artifactory.rp.konvery.work/artifactory/helm
    targetRevision: v0.*.*
  syncPolicy:
    automated: {}
