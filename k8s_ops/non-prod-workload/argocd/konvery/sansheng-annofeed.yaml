---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: sansheng-annofeed
  namespace: argocd
spec:
  destination:
    namespace: sansheng
    server: https://kubernetes.default.svc
  project: sansheng
  source:
    chart: sansheng-annofeed
    helm:
      parameters:
        - name: database.DATABASE
          value: sansheng-source
        - name: envVars.rpc\.iam\.addr
          value: iam:8001
        - name: envVars.rpc\.anno\.addr
          value: anno:8011
        - name: envVars.temporal\.addr
          value: temporal-frontend:7233
        - name: envVars.file_server\.svc_url
          value: https://anno.np.konvery.work/annofeed/v1
        - name: envVars.otel\.log\.level
          value: debug
        - name: envVars.file_server\.cloudfront\.enabled
          value: "true"
        - name: fullnameOverride
          value: annofeed
        - name: nameOverride
          value: annofeed
    repoURL: https://artifactory.rp.konvery.work/artifactory/helm
    targetRevision: 0.*.*
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
