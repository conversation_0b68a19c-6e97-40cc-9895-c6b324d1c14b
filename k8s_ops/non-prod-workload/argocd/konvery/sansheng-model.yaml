---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: sansheng-model
  namespace: argocd
spec:
  destination:
    namespace: sansheng
    server: https://kubernetes.default.svc
  project: sansheng
  source:
    chart: sansheng-unicorn
    helm:
      parameters:
      - name: serviceAccount.name
        value: sansheng-annofeed
      - name: envVars.ktwf\.worker\.service_account
        value: sansheng-annofeed
      - name: database.DATABASE
        value: sansheng-model
      - name: envVars.rpc\.iam\.addr
        value: iam:8001
      - name: envVars.temporal\.addr
        value: temporal-frontend:7233
      - name: envVars.otel\.log\.level
        value: debug
      - name: fullnameOverride
        value: model
      - name: nameOverride
        value: model
      values: |
        image:
          svcCommand: ["/app/unicorn", "--conf", "/data/conf/model.yaml", "model", "serve"]
          migrateCommand: ["/app/unicorn", "--conf", "/data/conf/model.yaml", "model", "migrate", "autoup"]
    repoURL: https://artifactory.rp.konvery.work/artifactory/helm
    targetRevision: 0.*.*
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
