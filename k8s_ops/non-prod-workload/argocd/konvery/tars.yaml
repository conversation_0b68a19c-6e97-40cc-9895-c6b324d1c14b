---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: tars
  namespace: argocd
spec:
  destination:
    namespace: sansheng
    server: https://kubernetes.default.svc
  project: sansheng
  source:
    chart: sansheng-unicorn
    helm:
      parameters:
        - name: service.httpPort
          value: '8070'
        - name: service.grpcPort
          value: '8071'
        - name: autoscaling.minReplicas
          value: '1'
        - name: autoscaling.maxReplicas
          value: '1'
        - name: envVars.otel\.log\.level
          value: debug
        - name: fullnameOverride
          value: tars
        - name: nameOverride
          value: tars
      values: |
        image:
          svcCommand: ["/app/unicorn", "--conf", "/data/conf/tars.yaml", "tars", "serve"]
          migrateCommand: ["/app/unicorn", "--conf", "/data/conf/tars.yaml", "tars", "migrate", "autoup"]
        serviceAccount:
          create: false
          name: sansheng-annofeed # sansheng-anyconn
          annotations:
            eks.amazonaws.com/role-arn: arn:aws-cn:iam::************:role/non-prod-workload-sansheng
        hostName: tars.np.konvery.work
        ingress:
          enabled: true
          className: "ingress-nginx-internal"
          annotations:
            # nginx.ingress.kubernetes.io/ssl-passthrough: "true"
            external-dns-annotation: ingress-nginx-internal
            nginx.ingress.kubernetes.io/proxy-body-size: 1024m
            nginx.ingress.kubernetes.io/enable-cors: "true"
          hosts:
            - host: tars.np.konvery.work
              paths:
                - path: /
                  pathType: ImplementationSpecific
          tls:
            - hosts:
              - tars.np.konvery.work
              secretName: wildcard-np-konvery-work
    repoURL: https://artifactory.rp.konvery.work/artifactory/helm
    targetRevision: v0.*.*
  syncPolicy:
    automated: {}
