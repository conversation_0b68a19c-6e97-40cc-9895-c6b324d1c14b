---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: loki
  namespace: argocd
spec:
  destination:
    namespace: monitoring
    server: https://kubernetes.default.svc
  project: infra
  source:
    chart: loki-stack
    helm:
      valuesObject:
        loki:
          image:
            repository: artifactory.rp.konvery.work/docker/grafana/loki
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                      - key: partition
                        operator: In
                        values:
                          - monitoring
          config:
            compactor:
              retention_enabled: true
          ingress:
            annotations:
              external-dns-annotation: ingress-nginx-internal
              nginx.ingress.kubernetes.io/backend-protocol: HTTPS
              nginx.ingress.kubernetes.io/ssl-passthrough: "true"
            enabled: false
            hosts:
              - host: loki-workload.np.konvery.work
                paths:
                  - /
            ingressClassName: ingress-nginx-internal
            tls:
              - hosts:
                  - loki-workload.np.konvery.work
                secretName: wildcard-np-konvery-work
          persistence:
            enabled: true
          tolerations:
            - effect: NoSchedule
              key: monitoring
              operator: Exists
        promtail:
          image:
            registry: artifactory.rp.konvery.work/docker
          enabled: true
          tolerations:
            - effect: NoSchedule
              key: monitoring
              operator: Exists
    repoURL: https://grafana.github.io/helm-charts
    targetRevision: 2.9.11
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
