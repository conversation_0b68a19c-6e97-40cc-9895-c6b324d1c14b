---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: sentry
  namespace: argocd
spec:
  destination:
    namespace: monitoring
    server: https://kubernetes.default.svc
  project: infra
  source:
    chart: sentry
    helm:
      valuesObject:
        clickhouse:
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                      - key: partition
                        operator: In
                        values:
                          - monitoring
          tolerations:
            - effect: NoSchedule
              key: monitoring
              operator: Exists
        externalPostgresql:
          existingSecret: sentry-role-secret
          existingSecretKey: password
          host: non-prod-workload-postgresql.cluster-ck7i7eqx0frz.rds.cn-northwest-1.amazonaws.com.cn
          sslMode: disable
          username: sentry
        filestore:
          backend: s3
          s3:
            bucketName: non-prod-workload-sentry
            region_name: cn-northwest-1
        ingress:
          annotations:
            external-dns-annotation: ingress-nginx-internal
            nginx.ingress.kubernetes.io/ssl-passthrough: "true"
            nginx.ingress.kubernetes.io/use-regex: "true"
          enabled: true
          hostname: sentry.np.konvery.work
          ingressClassName: ingress-nginx-internal
          tls:
            - hosts:
                - sentry.np.konvery.work
              secretName: wildcard-np-konvery-work
        kafka:
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                      - key: partition
                        operator: In
                        values:
                          - monitoring
          tolerations:
            - effect: NoSchedule
              key: monitoring
              operator: Exists
        nginx:
          enabled: false
        postgresql:
          enabled: false
        rabbitmq:
          enabled: false
        redis:
          master:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
          replica:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
        relay:
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                      - key: partition
                        operator: In
                        values:
                          - monitoring
          tolerations:
            - effect: NoSchedule
              key: monitoring
              operator: Exists
        sentry:
          billingMetricsConsumer:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
          cleanup:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
          cron:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
          ingestConsumer:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
          ingestMetricsConsumerPerf:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
          ingestMetricsConsumerRh:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
          ingestReplayRecordings:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
          subscriptionConsumerEvents:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
          subscriptionConsumerTransactions:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
          postProcessForwardErrors:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
          postProcessForwardTransactions:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
          web:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
          worker:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
        snuba:
          api:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            resources:
              limits:
                cpu: 1000m
                memory: 1500Mi
              requests:
                cpu: 100m
                memory: 400Mi
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
          consumer:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
          outcomesConsumer:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
          replacer:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
          replaysConsumer:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
          sessionsConsumer:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
          subscriptionConsumerEvents:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
          subscriptionConsumerSessions:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
          subscriptionConsumerTransactions:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
          transactionsConsumer:
            affinity:
              nodeAffinity:
                requiredDuringSchedulingIgnoredDuringExecution:
                  nodeSelectorTerms:
                    - matchExpressions:
                        - key: partition
                          operator: In
                          values:
                            - monitoring
            tolerations:
              - effect: NoSchedule
                key: monitoring
                operator: Exists
        serviceAccount:
          annotations:
            eks.amazonaws.com/role-arn: arn:aws-cn:iam::************:role/non-prod-workload-sentry
        system:
          secretKey: pwE2H1m0FLVVYL4c
        user:
          email: <EMAIL>
          password: n45K6PZIkDnDgDj3
          username: rd
        zookeeper:
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                      - key: partition
                        operator: In
                        values:
                          - monitoring
          tolerations:
            - effect: NoSchedule
              key: monitoring
              operator: Exists
    repoURL: https://sentry-kubernetes.github.io/charts
    targetRevision: 17.12.0
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true