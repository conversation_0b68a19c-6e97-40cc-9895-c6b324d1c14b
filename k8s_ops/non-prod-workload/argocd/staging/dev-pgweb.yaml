apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: staging-pgweb
  namespace: argocd
spec:
  destination:
    namespace: sansheng
    server: https://kubernetes.default.svc
  project: sansheng
  source:
    chart: pgweb
    helm:
      parameters:
        - name: secretDBName
          value: staging-sansheng-role-secret
        - name: secretKetoDBName
          # it is not the correct secret for keto, put it here to make it startup
          value: staging-sansheng-role-secret
      values: |
        ingress:
          enabled: true
          className: "ingress-nginx-internal"
          annotations:
            # nginx.ingress.kubernetes.io/ssl-passthrough: "true"
            external-dns-annotation: ingress-nginx-internal
            # nginx.ingress.kubernetes.io/proxy-body-size: 1024m
            # nginx.ingress.kubernetes.io/enable-cors: "true"
          hosts:
            # todo: should change to the actual host when make deployment
            - host: staging-pgweb.np.konvery.work
              paths:
                - path: /
                  pathType: ImplementationSpecific
          tls:
            - secretName: wildcard-np-konvery-work
              hosts:
                - staging-pgweb.np.konvery.work
    repoURL: https://artifactory.rp.konvery.work/artifactory/helm
    targetRevision: 0.1.*
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
