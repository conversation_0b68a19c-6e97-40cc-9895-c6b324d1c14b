apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: staging-etcd
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: staging-sansheng
  destination:
    namespace: staging-sansheng
    server: https://kubernetes.default.svc
  source:
    repoURL: https://charts.bitnami.com/bitnami
    targetRevision: 8.5.5
    chart: etcd
    helm:
      values: |
        auth:
          rbac:
            allowNoneAuthentication: false
            enable: true
            rootPassword: IP8TPVsfKO0diwa6
          token:
            type: simple
        autoCompactionMode: periodic
        autoCompactionRetention: 10m
        # extraEnvVars:
        #   - name: ETCD_HEARTBEAT_INTERVAL
        #     value: '500'
        # 'new' or 'existing'
        initialClusterState: existing
        logLevel: debug
        removeMemberOnContainerTermination: false
        replicaCount: 3
        updateStrategy:
          type: OnDelete
  syncPolicy:
    automated:
      selfHeal: true
      prune: true
    syncOptions:
    - CreateNamespace=true
