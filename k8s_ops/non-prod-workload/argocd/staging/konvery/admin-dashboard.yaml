---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: staging-admin-dashboard
  namespace: argocd
spec:
  destination:
    namespace: staging-sansheng
    server: https://kubernetes.default.svc
  project: staging-sansheng
  source:
    chart: manage-dashboard
    helm:
      parameters:
        - name: fullnameOverride
          value: staging-admin-dashboard
        - name: nameOverride
          value: staging-admin-dashboard
      values: |
        envVars:
          NGINX_ROOT: "/usr/share/nginx/html/staging"
          NGINX_ENVSUBST_OUTPUT_DIR: "/etc/nginx/konvery.conf.d"
    repoURL: https://artifactory.rp.konvery.work/artifactory/helm
    targetRevision: 0.4.5
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
