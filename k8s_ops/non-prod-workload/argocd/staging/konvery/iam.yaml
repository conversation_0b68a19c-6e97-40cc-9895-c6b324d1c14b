---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: staging-iam
  namespace: argocd
spec:
  destination:
    namespace: staging-sansheng
    server: https://kubernetes.default.svc
  project: staging-sansheng
  source:
    chart: iam
    helm:
      parameters:
        - name: secretDBName
          value: staging-sansheng-role-secret
        - name: database.DATABASE
          value: staging-sansheng-usercenter
        - name: envVars.danger_test\.fake_logins\.dev\.phone_regexp
          value: ^\\+0[0-9]{4}
        - name: envVars.danger_test\.fake_logins\.dev\.auth_code
          value: "123456"
        - name: envVars.alisms\.access_key
          value: LTAI5tDfFxa6g7dJAmCd4TED
        - name: envVars.alisms\.secret_key
          value: ******************************
        - name: envVars.jwt\.sign_key
          value: konvery-test-key
        - name: envVars.server\.cookie_domain
          value: staging-anno.np.konvery.work
        - name: envVars.keto\.read_addr
          value: staging-keto-read:80
        - name: envVars.keto\.write_addr
          value: staging-keto-write:80
        - name: envVars.otel\.log\.level
          value: debug
    repoURL: https://artifactory.rp.konvery.work/artifactory/helm
    targetRevision: 0.4.1
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
