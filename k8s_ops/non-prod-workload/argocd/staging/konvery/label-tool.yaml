---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: staging-label-tool
  namespace: argocd
spec:
  destination:
    namespace: staging-sansheng
    server: https://kubernetes.default.svc
  project: staging-sansheng
  source:
    chart: label-tool
    helm:
      values: |
        envVars:
          NGINX_ROOT: "/usr/share/nginx/html/staging"
          NGINX_ENVSUBST_OUTPUT_DIR: "/etc/nginx/konvery.conf.d"
    repoURL: https://artifactory.rp.konvery.work/artifactory/helm
    targetRevision: 0.3.65
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
