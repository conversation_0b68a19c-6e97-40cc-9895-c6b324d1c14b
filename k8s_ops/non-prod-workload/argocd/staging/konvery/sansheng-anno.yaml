---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: staging-sansheng-anno
  namespace: argocd
spec:
  destination:
    namespace: staging-sansheng
    server: https://kubernetes.default.svc
  project: staging-sansheng
  source:
    chart: sansheng-anno
    helm:
      parameters:
        - name: secretDBName
          value: staging-sansheng-role-secret
        - name: secretS3
          value: staging-sansheng-iam-s3
        - name: secretCloudfront
          value: staging-sansheng-cloudfront
        - name: database.DATABASE
          value: staging-sansheng-core
        - name: envVars.rpc\.iam\.addr
          value: staging-iam:8001
        - name: envVars.rpc\.annout\.addr
          value: staging-annout:8031
        - name: envVars.rpc\.annofeed\.addr
          value: staging-annofeed:8021
        - name: envVars.temporal\.addr
          value: staging-temporal-frontend:7233
        - name: envVars.otel\.log\.level
          value: debug
        - name: envVars.mq\.producer\.topic
          value: "arn:aws-cn:sns:cn-northwest-1:035532479701:non-prod-workload-sns-sansheng-anno-staging"
        - name: envVars.mq\.sqs\.name
          value: non-prod-workload-sqs-sansheng-anno-staging
        - name: fullnameOverride
          value: staging-anno
        - name: nameOverride
          value: staging-anno
    repoURL: https://artifactory.rp.konvery.work/artifactory/helm
    targetRevision: 0.4.6
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
