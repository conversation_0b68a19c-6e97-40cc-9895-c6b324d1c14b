---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: staging-sansheng-annofeed
  namespace: argocd
spec:
  destination:
    namespace: staging-sansheng
    server: https://kubernetes.default.svc
  project: staging-sansheng
  source:
    chart: sansheng-annofeed
    helm:
      parameters:
        - name: serviceAcount.name
          value: staging-sansheng-annofeed
        - name: secretS3
          value: staging-sansheng-iam-s3
        - name: secretDBName
          value: staging-sansheng-role-secret
        - name: database.DATABASE
          value: staging-sansheng-source
        - name: secretCloudfront
          value: staging-sansheng-cloudfront
        - name: envVars.rpc\.iam\.addr
          value: staging-iam:8001
        - name: envVars.rpc\.anno\.addr
          value: staging-anno:8011
        - name: envVars.temporal\.addr
          value: staging-temporal-frontend:7233
        - name: envVars.file_server\.svc_url
          value: https://staging-anno.np.konvery.work/annofeed/v1
        - name: envVars.otel\.log\.level
          value: debug
        - name: envVars.file_server\.cloudfront\.enabled
          value: "true"
        - name: envVars.mq\.producer\.topic
          value: "arn:aws-cn:sns:cn-northwest-1:035532479701:non-prod-workload-sns-sansheng-annofeed-staging"
        - name: envVars.mq\.sqs\.name
          value: non-prod-workload-sqs-sansheng-annofeed-staging
        - name: fullnameOverride
          value: staging-annofeed
        - name: nameOverride
          value: staging-annofeed
    repoURL: https://artifactory.rp.konvery.work/artifactory/helm
    targetRevision: 0.4.6
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
