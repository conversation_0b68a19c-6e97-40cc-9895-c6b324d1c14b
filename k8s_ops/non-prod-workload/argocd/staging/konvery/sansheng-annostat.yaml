apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: staging-sansheng-annostat
  namespace: argocd
spec:
  destination:
    namespace: staging-sansheng
    server: https://kubernetes.default.svc
  project: staging-sansheng
  source:
    chart: sansheng-annostat
    helm:
      parameters:
        - name: secretDBName
          value: staging-sansheng-role-secret
        - name: database.DATABASE
          value: staging-sansheng-annostat
        - name: envVars.rpc\.iam\.addr
          value: staging-iam:8001
        - name: envVars.rpc\.anno\.addr
          value: staging-anno:8011
        - name: envVars.rpc\.annofeed\.addr
          value: staging-annofeed:8021
        - name: envVars.temporal\.addr
          value: staging-temporal-frontend:7233
        - name: envVars.otel\.log\.level
          value: debug
        - name: envVars.mq\.producer\.topic
          value: "arn:aws-cn:sns:cn-northwest-1:035532479701:non-prod-workload-sns-sansheng-annostat-staging"
        - name: envVars.mq\.sqs\.name
          value: non-prod-workload-sqs-sansheng-annostat-staging
        - name: fullnameOverride
          value: staging-annostat
        - name: nameOverride
          value: staging-annostat
    repoURL: https://artifactory.rp.konvery.work/artifactory/helm
    targetRevision: 0.4.1
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
