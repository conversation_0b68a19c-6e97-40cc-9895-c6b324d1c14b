---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: staging-sansheng-annout
  namespace: argocd
spec:
  destination:
    namespace: staging-sansheng
    server: https://kubernetes.default.svc
  project: staging-sansheng
  source:
    chart: sansheng-annout
    helm:
      parameters:
        - name: secretDBName
          value: staging-sansheng-role-secret
        - name: secretS3
          value: staging-sansheng-iam-s3
        - name: database.DATABASE
          value: staging-sansheng-export
        - name: envVars.rpc\.iam\.addr
          value: staging-iam:8001
        - name: envVars.rpc\.anno\.addr
          value: staging-anno:8011
        - name: envVars.rpc\.annofeed\.addr
          value: staging-annofeed:8021
        - name: envVars.temporal\.addr
          value: staging-temporal-frontend:7233
        - name: envVars.otel\.log\.level
          value: debug
        - name: fullnameOverride
          value: staging-annout
        - name: nameOverride
          value: staging-annout
    repoURL: https://artifactory.rp.konvery.work/artifactory/helm
    targetRevision: 0.4.2
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
