apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: staging-temporal
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: staging-sansheng
  destination:
    namespace: staging-sansheng
    server: https://kubernetes.default.svc
  source:
    repoURL: https://artifactory.rp.konvery.work/artifactory/helm
    targetRevision: 0.20.2
    chart: temporal
    helm:
      values: |
        server:
          config:
            # numHistoryShards: 2000
            persistence:
              default:
                driver: "sql"
                sql:
                  driver: "postgres"
                  host: "non-prod-workload-postgresql.cluster-ck7i7eqx0frz.rds.cn-northwest-1.amazonaws.com.cn"
                  port: "5432"
                  database: staging_temporal
                  user: "staging-temporal"
                  existingSecret: "staging-temporal-role-secret"
                  maxConns: 128
              visibility:
                driver: "sql"
                sql:
                  driver: "postgres"
                  host: "non-prod-workload-postgresql.cluster-ck7i7eqx0frz.rds.cn-northwest-1.amazonaws.com.cn"
                  port: "5432"
                  database: staging_temporal_visibility
                  user: "staging-temporal"
                  existingSecret: "staging-temporal-role-secret"
            auth:
              allowNoAuth: true
        schema:
          setup:
            enabled: false
          update:
            enabled: false
        cassandra:
          enabled: false
        mysql:
          enabled: false    
        prometheus:
          enabled: false
        grafana:
          enabled: false
        elasticsearch:
          enabled: false
        web:
          ingress:
            enabled: false
        serviceAccount:
          create: true
  syncPolicy:
    automated:
      selfHeal: true
      prune: true
    syncOptions:
    - CreateNamespace=true
