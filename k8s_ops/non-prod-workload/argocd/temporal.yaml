apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: temporal
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: sansheng
  destination:
    namespace: sansheng
    server: https://kubernetes.default.svc
  source:
    repoURL: https://artifactory.rp.konvery.work/artifactory/helm
    targetRevision: 0.20.2
    chart: temporal
    helm:
      values: |
        server:
          config:
            # numHistoryShards: 2000
            persistence:
              default:
                driver: "sql"
                sql:
                  driver: "postgres"
                  host: "non-prod-workload-postgresql.cluster-ck7i7eqx0frz.rds.cn-northwest-1.amazonaws.com.cn"
                  port: "5432"
                  database: temporal
                  user: "temporal"
                  existingSecret: "temporal-role-secret"
                  maxConns: 128
              visibility:
                driver: "sql"
                sql:
                  driver: "postgres"
                  host: "non-prod-workload-postgresql.cluster-ck7i7eqx0frz.rds.cn-northwest-1.amazonaws.com.cn"
                  port: "5432"
                  database: temporal_visibility
                  user: "temporal"
                  existingSecret: "temporal-role-secret"
            auth:
              allowNoAuth: true
        schema:
          setup:
            enabled: false
          update:
            enabled: false
        cassandra:
          enabled: false
        mysql:
          enabled: false    
        prometheus:
          enabled: false
        grafana:
          enabled: false
        elasticsearch:
          enabled: false
        web:
          ingress:
            enabled: false
        serviceAccount:
          create: true
  syncPolicy:
    automated:
      selfHeal: true
      prune: true
    syncOptions:
    - CreateNamespace=true
