---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: wildcard-np-konvery-work
  namespace: infra
spec:
  dnsNames:
  - '*.np.konvery.work'
  issuerRef:
    kind: ClusterIssuer
    name: letsencrypt-np-konvery-work
  secretName: wildcard-np-konvery-work
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: wildcard-np-konvery-work
  namespace: argocd
spec:
  dnsNames:
  - '*.np.konvery.work'
  issuerRef:
    kind: ClusterIssuer
    name: letsencrypt-np-konvery-work
  secretName: wildcard-np-konvery-work
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: wildcard-np-konvery-work
  namespace: sansheng
spec:
  dnsNames:
  - '*.np.konvery.work'
  issuerRef:
    kind: ClusterIssuer
    name: letsencrypt-np-konvery-work
  secretName: wildcard-np-konvery-work
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: wildcard-np-konvery-work
  namespace: monitoring
spec:
  dnsNames:
  - '*.np.konvery.work'
  issuerRef:
    kind: ClusterIssuer
    name: letsencrypt-np-konvery-work
  secretName: wildcard-np-konvery-work
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: wildcard-np-konvery-work
  namespace: staging-sansheng
spec:
  dnsNames:
  - '*.np.konvery.work'
  issuerRef:
    kind: ClusterIssuer
    name: letsencrypt-np-konvery-work
  secretName: wildcard-np-konvery-work
