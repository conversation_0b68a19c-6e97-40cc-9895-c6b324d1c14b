apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    # nginx.ingress.kubernetes.io/backend-protocol: HTTP
    # nginx.ingress.kubernetes.io/ssl-passthrough: "false"
    # nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: 4096m
    external-dns-annotation: ingress-nginx-internal
  name: apisix-anno
  namespace: sansheng
spec:
  ingressClassName: ingress-nginx-internal
  rules:
  - host: anno.np.konvery.work
    http:
      paths:
      - backend:
          service: 
            name: apisix-gateway
            port: 
              name: apisix-gateway
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - anno.np.konvery.work
    secretName: wildcard-np-konvery-work