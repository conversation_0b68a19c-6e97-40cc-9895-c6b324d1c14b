apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    # nginx.ingress.kubernetes.io/backend-protocol: HTTP
    # nginx.ingress.kubernetes.io/ssl-passthrough: "false"
    external-dns-annotation: ingress-nginx-internal
  name: apisix-label
  namespace: sansheng
spec:
  ingressClassName: ingress-nginx-internal
  rules:
  - host: label.np.konvery.work
    http:
      paths:
      - backend:
          service: 
            name: apisix-gateway
            port: 
              name: apisix-gateway
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - label.np.konvery.work
    secretName: wildcard-np-konvery-work