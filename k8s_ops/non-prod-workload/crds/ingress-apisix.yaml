apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    # kubernetes.io/ingress.class: nginx
    # nginx.ingress.kubernetes.io/backend-protocol: HTTPS
    nginx.ingress.kubernetes.io/ssl-passthrough: "true"
    external-dns-annotation: ingress-nginx-internal
  name: apisix-dashboard
  namespace: sansheng
spec:
  ingressClassName: ingress-nginx-internal
  rules:
  - host: apisix-dashboard.np.konvery.work
    http:
      paths:
      - backend:
          service: 
            name: apisix-dashboard
            port: 
              name: http
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - apisix-dashboard.np.konvery.work
    secretName: wildcard-np-konvery-work
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    # kubernetes.io/ingress.class: nginx
    # nginx.ingress.kubernetes.io/backend-protocol: HTTPS
    nginx.ingress.kubernetes.io/ssl-passthrough: "true"
    external-dns-annotation: ingress-nginx-internal
  name: apisixadmin
  namespace: sansheng
spec:
  ingressClassName: ingress-nginx-internal
  rules:
  - host: apisix-admin.np.konvery.work
    http:
      paths:
      - backend:
          service: 
            name: apisix-admin
            port: 
              number: 9180 
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - apisix-admin.np.konvery.work
    secretName: wildcard-np-konvery-work
