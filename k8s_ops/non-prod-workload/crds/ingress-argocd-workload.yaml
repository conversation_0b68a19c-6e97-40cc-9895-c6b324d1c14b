---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-np-konvery-work
    # kubernetes.io/ingress.class: nginx
    external-dns-annotation: ingress-nginx-internal
    kubernetes.io/tls-acme: 'true'
    nginx.ingress.kubernetes.io/backend-protocol: HTTPS
    # nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/ssl-passthrough: 'true'
  name: argocd
  namespace: argocd
spec:
  ingressClassName: ingress-nginx-internal
  rules:
    - host: argocd-workload.np.konvery.work
      http:
        paths:
          - backend:
              service:
                name: argocd-server
                port:
                  name: https
            path: /
            pathType: Prefix
  tls:
    - hosts:
      - argocd-workload.np.konvery.work
      # secretName: wildcard-np-konvery-work
      secretName: argocd-secret
