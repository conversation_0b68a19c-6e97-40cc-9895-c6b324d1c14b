---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    external-dns-annotation: ingress-nginx-internal
    # kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/backend-protocol: HTTP
    nginx.ingress.kubernetes.io/ssl-passthrough: 'true'
  name: prometheus-workload
  namespace: monitoring
spec:
  ingressClassName: ingress-nginx-internal
  rules:
    - host: prometheus-workload.np.konvery.work
      http:
        paths:
          - backend:
              service:
                name: prometheus-k8s
                port:
                  name: web
            path: /
            pathType: Prefix
  tls:
    - hosts:
        - prometheus-workload.np.konvery.work
      secretName: wildcard-np-konvery-work
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    external-dns-annotation: ingress-nginx-internal
    # kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/backend-protocol: HTTP
    nginx.ingress.kubernetes.io/ssl-passthrough: 'true'
  name: grafana-workload
  namespace: monitoring
spec:
  ingressClassName: ingress-nginx-internal
  rules:
    - host: grafana-workload.np.konvery.work
      http:
        paths:
          - backend:
              service:
                name: grafana
                port:
                  name: http
            path: /
            pathType: Prefix
  tls:
    - hosts:
        - grafana-workload.np.konvery.work
      secretName: wildcard-np-konvery-work