apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: non-prod-workload-mysql
  namespace: crossplane-system
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: external-secrets
    kind: ClusterSecretStore
  target:
    name: non-prod-workload-mysql
    creationPolicy: Owner
    template:
      data:
        endpoint: "{{ .endpoint | toString }}"
        password: "{{ .password | toString }}"
        username: "{{ .username | toString }}"
        port: "3306"
  dataFrom:
    - extract:
        key: non-prod-workload-mysql
