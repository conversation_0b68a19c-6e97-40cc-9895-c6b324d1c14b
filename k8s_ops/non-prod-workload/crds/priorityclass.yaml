---
apiVersion: scheduling.k8s.io/v1
description: This priority class should be used for critical service only.
globalDefault: false
kind: PriorityClass
metadata:
  name: infra-node-critical
value: 128
---
apiVersion: scheduling.k8s.io/v1
description: This priority class should be used for critical service only.
globalDefault: false
kind: PriorityClass
metadata:
  name: workload-node-critical
value: 64
---
apiVersion: scheduling.k8s.io/v1
description: The default priority class.
globalDefault: true
kind: PriorityClass
metadata:
  name: default
value: 0
