---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: sansheng-annofeed
  namespace: sansheng
rules:
  - apiGroups: [""]
    resources: ["serviceaccounts"]
    verbs: ["get"]
  - apiGroups: [""]
    resources: ["deployments", "jobs", "pods","persistentvolumes", "persistentvolumeclaims"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
  - apiGroups: ["batch"]
    resources: ["deployments", "jobs", "pods"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
  - apiGroups: ["networking.k8s.io"]
    resources: ["networkpolicies"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: sansheng-annofeed
  namespace: sansheng
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: sansheng-annofeed
subjects:
  - kind: ServiceAccount
    name: sansheng-annofeed
    namespace: sansheng
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: sansheng-annofeed
  namespace: sansheng-sandbox
rules:
  - apiGroups: [""]
    resources: ["serviceaccounts"]
    verbs: ["get"]
  - apiGroups: [""]
    resources: ["deployments", "jobs", "pods","persistentvolumes", "persistentvolumeclaims"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
  - apiGroups: ["batch"]
    resources: ["deployments", "jobs", "pods"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
  - apiGroups: ["networking.k8s.io"]
    resources: ["networkpolicies"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: sansheng-annofeed
  namespace: sansheng-sandbox
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: sansheng-annofeed
subjects:
  - kind: ServiceAccount
    name: sansheng-annofeed
    namespace: sansheng
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: sansheng-annofeed
rules:
  - apiGroups: [""]
    resources: ["persistentvolumes", "persistentvolumeclaims", "pods/log"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: sansheng-annofeed
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: sansheng-annofeed
subjects:
  - kind: ServiceAccount
    name: sansheng-annofeed
    namespace: sansheng
