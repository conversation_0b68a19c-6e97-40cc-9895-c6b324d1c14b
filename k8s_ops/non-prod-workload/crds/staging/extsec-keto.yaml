apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: staging-keto
  namespace: staging-sansheng
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: staging-external-secrets-kubernetes
    kind: ClusterSecretStore
  target:
    name: staging-keto
    creationPolicy: Owner
    template: 
      data:
        dsn: postgres://{{ .username | toString }}:{{ .password | toString }}@{{ .endpoint | toString }}:{{ .port | toString }}/staging-keto?max_conns=64&max_idle_conns=16&max_conn_lifetime=8h&max_conn_idle_time=1h
  dataFrom:
    - extract:
        key: staging-keto-role-secret
