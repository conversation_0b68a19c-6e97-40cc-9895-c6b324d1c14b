apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    # nginx.ingress.kubernetes.io/backend-protocol: HTTP
    # nginx.ingress.kubernetes.io/ssl-passthrough: "false"
    # nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: 4096m
    external-dns-annotation: ingress-nginx-internal
  name: staging-apisix-anno
  namespace: staging-sansheng
spec:
  ingressClassName: ingress-nginx-internal
  rules:
  - host: staging-anno.np.konvery.work
    http:
      paths:
      - backend:
          service: 
            name: staging-apisix-gateway
            port: 
              name: apisix-gateway
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - staging-anno.np.konvery.work
    secretName: wildcard-np-konvery-work
