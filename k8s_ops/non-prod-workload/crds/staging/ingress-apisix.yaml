apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    # kubernetes.io/ingress.class: nginx
    # nginx.ingress.kubernetes.io/backend-protocol: HTTPS
    nginx.ingress.kubernetes.io/ssl-passthrough: "true"
    external-dns-annotation: ingress-nginx-internal
  name: staging-apisix-dashboard
  namespace: staging-sansheng
spec:
  ingressClassName: ingress-nginx-internal
  rules:
  - host: staging-apisix-dashboard.np.konvery.work
    http:
      paths:
      - backend:
          service: 
            name: staging-apisix-dashboard
            port: 
              name: http
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - staging-apisix-dashboard.np.konvery.work
    secretName: wildcard-np-konvery-work
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    # kubernetes.io/ingress.class: nginx
    # nginx.ingress.kubernetes.io/backend-protocol: HTTPS
    nginx.ingress.kubernetes.io/ssl-passthrough: "true"
    external-dns-annotation: ingress-nginx-internal
  name: staging-apisixadmin
  namespace: staging-sansheng
spec:
  ingressClassName: ingress-nginx-internal
  rules:
  - host: staging-apisix-admin.np.konvery.work
    http:
      paths:
      - backend:
          service: 
            name: staging-apisix-admin
            port: 
              number: 9180 
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - staging-apisix-admin.np.konvery.work
    secretName: wildcard-np-konvery-work
