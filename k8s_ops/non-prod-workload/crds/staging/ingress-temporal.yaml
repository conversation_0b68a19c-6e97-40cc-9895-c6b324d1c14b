apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    # kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/backend-protocol: HTTP
    # nginx.ingress.kubernetes.io/ssl-passthrough: "true"
    external-dns-annotation: ingress-nginx-internal
  name: staging-temporal
  namespace: staging-sansheng
spec:
  ingressClassName: ingress-nginx-internal
  rules:
  - host: staging-temporal.np.konvery.work
    http:
      paths:
      - backend:
          service: 
            name: staging-temporal-web
            port: 
              number: 8080
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - staging-temporal.np.konvery.work
    secretName: wildcard-np-konvery-work