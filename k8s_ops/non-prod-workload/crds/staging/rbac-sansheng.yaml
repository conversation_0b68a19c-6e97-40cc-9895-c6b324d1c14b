---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: staging-sansheng-annofeed
  namespace: staging-sansheng
rules:
  - apiGroups: [""]
    resources: ["serviceaccounts"]
    verbs: ["get"]
  - apiGroups: [""]
    resources: ["deployments", "jobs", "pods"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
  - apiGroups: ["batch"]
    resources: ["deployments", "jobs", "pods"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: staging-sansheng-annofeed
  namespace: staging-sansheng
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: staging-sansheng-annofeed
subjects:
  - kind: ServiceAccount
    name: staging-sansheng-annofeed
    namespace: staging-sansheng
