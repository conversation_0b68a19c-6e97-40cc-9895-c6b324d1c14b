---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: staging-temporal
  namespace: staging-sansheng
rules:
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: staging-temporal
  namespace: staging-sansheng
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: staging-temporal
subjects:
  - kind: ServiceAccount
    name: staging-temporal
    namespace: staging-sansheng
