---
apiVersion: mysql.sql.crossplane.io/v1alpha1
kind: Grant
metadata:
  name: provider-grant-cadence
spec:
  forProvider:
    privileges:
      # - ALTER
      # - CREATE
      # - DELETE
      # - DROP
      # - INSERT
      # - SELECT
      # - UPDATE
      - ALL
    userRef:
      name: cadence
    databaseRef:
      name: cadence
  providerConfigRef:
    name: non-prod-workload-crossplane-provider-mysql-config
---
apiVersion: mysql.sql.crossplane.io/v1alpha1
kind: Grant
metadata:
  name: provider-grant-cadence-visibility
spec:
  forProvider:
    privileges:
      # - ALTER
      # - CREATE
      # - DELETE
      # - DROP
      # - INSERT
      # - SELECT
      # - UPDATE
      - ALL
    userRef:
      name: cadence
    databaseRef:
      name: cadence-visibility
  providerConfigRef:
    name: non-prod-workload-crossplane-provider-mysql-config
