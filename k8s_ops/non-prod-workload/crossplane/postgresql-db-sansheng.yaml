---
apiVersion: postgresql.sql.crossplane.io/v1alpha1
kind: Database
metadata:
  name: sansheng-source
spec:
  deletionPolicy: Delete
  forProvider: {}
  providerConfigRef:
    name: provider-sql-sansheng
---
apiVersion: postgresql.sql.crossplane.io/v1alpha1
kind: Database
metadata:
  name: sansheng-export
spec:
  deletionPolicy: Delete
  forProvider: {}
  providerConfigRef:
    name: provider-sql-sansheng
---
apiVersion: postgresql.sql.crossplane.io/v1alpha1
kind: Database
metadata:
  name: sansheng-core
spec:
  deletionPolicy: Delete
  forProvider: {}
  providerConfigRef:
    name: provider-sql-sansheng
---
apiVersion: postgresql.sql.crossplane.io/v1alpha1
kind: Database
metadata:
  name: sansheng-usercenter
spec:
  deletionPolicy: Delete
  forProvider: {}
  providerConfigRef:
    name: provider-sql-sansheng
---
apiVersion: postgresql.sql.crossplane.io/v1alpha1
kind: Database
metadata:
  name: sansheng-annostat
spec:
  deletionPolicy: Delete
  forProvider: {}
  providerConfigRef:
    name: provider-sql-sansheng
---
apiVersion: postgresql.sql.crossplane.io/v1alpha1
kind: Database
metadata:
  name: sansheng-model
spec:
  deletionPolicy: Delete
  forProvider: {}
  providerConfigRef:
    name: provider-sql-sansheng
