---
apiVersion: postgresql.sql.crossplane.io/v1alpha1
kind: Database
metadata:
  name: temporal
spec:
  deletionPolicy: Delete
  forProvider: {}
  providerConfigRef:
    name: provider-sql-temporal
---
apiVersion: postgresql.sql.crossplane.io/v1alpha1
kind: Database
metadata:
  name: temporal-visibility
  annotations:
    crossplane.io/external-name: temporal_visibility
spec:
  deletionPolicy: Delete
  forProvider: {}
  providerConfigRef:
    name: provider-sql-temporal
