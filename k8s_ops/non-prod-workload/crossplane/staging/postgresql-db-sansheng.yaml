---
apiVersion: postgresql.sql.crossplane.io/v1alpha1
kind: Database
metadata:
  name: staging-sansheng-source
spec:
  deletionPolicy: Delete
  forProvider: {}
  providerConfigRef:
    name: staging-provider-sql-sansheng
---
apiVersion: postgresql.sql.crossplane.io/v1alpha1
kind: Database
metadata:
  name: staging-sansheng-export
spec:
  deletionPolicy: Delete
  forProvider: {}
  providerConfigRef:
    name: staging-provider-sql-sansheng
---
apiVersion: postgresql.sql.crossplane.io/v1alpha1
kind: Database
metadata:
  name: staging-sansheng-core
spec:
  deletionPolicy: Delete
  forProvider: {}
  providerConfigRef:
    name: staging-provider-sql-sansheng
---
apiVersion: postgresql.sql.crossplane.io/v1alpha1
kind: Database
metadata:
  name: staging-sansheng-usercenter
spec:
  deletionPolicy: Delete
  forProvider: {}
  providerConfigRef:
    name: staging-provider-sql-sansheng
---
apiVersion: postgresql.sql.crossplane.io/v1alpha1
kind: Database
metadata:
  name: staging-sansheng-annostat
spec:
  deletionPolicy: Delete
  forProvider: {}
  providerConfigRef:
    name: staging-provider-sql-sansheng
