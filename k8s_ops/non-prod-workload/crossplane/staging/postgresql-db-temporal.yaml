---
apiVersion: postgresql.sql.crossplane.io/v1alpha1
kind: Database
metadata:
  name: staging-temporal
  annotations:
    crossplane.io/external-name: staging_temporal
spec:
  deletionPolicy: Delete
  forProvider: {}
  providerConfigRef:
    name: staging-provider-sql-temporal
---
apiVersion: postgresql.sql.crossplane.io/v1alpha1
kind: Database
metadata:
  name: staging-temporal-visibility
  annotations:
    crossplane.io/external-name: staging_temporal_visibility
spec:
  deletionPolicy: Delete
  forProvider: {}
  providerConfigRef:
    name: staging-provider-sql-temporal
