---
apiVersion: postgresql.sql.crossplane.io/v1alpha1
kind: Role
metadata:
  name: staging-keto
spec:
  deletionPolicy: Delete
  forProvider:
    connectionLimit: 256
    privileges:
      login: true
      createDb: true
  providerConfigRef:
    name: non-prod-workload-crossplane-provider-postgresql-config
  writeConnectionSecretToRef:
    name: staging-keto-role-secret
    namespace: staging-sansheng
