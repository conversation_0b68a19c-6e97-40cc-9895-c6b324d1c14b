stringData:
  grafana.ini: |
    [server]
    root_url = https://grafana-workload.np.konvery.work
    [auth.generic_oauth]
    name = Keycloak
    enabled = true
    allow_sign_up = true
    client_id = grafana-workload-np
    client_secret = 0hdbJhmeNsOFS1eOG78NabmAXF4WHoSv
    scopes = openid roles profile email web-origins
    allow_assign_grafana_admin = true
    auth_url = https://keycloak.rp.konvery.work/realms/konvery/protocol/openid-connect/auth
    token_url = https://keycloak.rp.konvery.work/realms/konvery/protocol/openid-connect/token
    api_url = https://keycloak.rp.konvery.work/realms/konvery/protocol/openid-connect/userinfo
    role_attribute_path = "contains(realm_access.roles[*], 'kvy-admin') && 'Admin' || contains(realm_access.roles[*], 'kvy-editor') && 'Editor' || 'Viewer'"
    [date_formats]
    default_timezone = Asia/Shanghai
    [security]
    admin_password = QCW48mskdwEAUUaW
    [users]
    auto_assign_org = true
