# Production infra cluster setup

## Preparing Environment

```shell
aws eks describe-cluster --name prod-infra-cluster --query "cluster.identity.oidc.issuer" --output text
https://oidc.eks.cn-northwest-1.amazonaws.com.cn/id/D992B10663F789CF922194F3931BC851
```

```shell
aws eks update-kubeconfig --region cn-northwest-1 --name prod-infra-cluster
```

## Metrics server

```shell
# https://docs.aws.amazon.com/eks/latest/userguide/metrics-server.html
# kubectl apply -f https://github.com/kubernetes-sigs/metrics-server/releases/latest/download/components.yaml
kaf k8s_ops/misc/metrics-server.yaml

kubectl -n kube-system patch deployment metrics-server --patch-file k8s_ops/prod-infra/patch/metrics-server.yaml
```

### Update cluster role, user, and user group

```shell
# Update cluster role
kubectl apply -f k8s_ops/misc/cluster-role/kvy-clusterrole.yaml
# Cluster role bindings
kubectl apply -f k8s_ops/misc/cluster-role/cluster-role-binding.yaml

# Patch aws-auth to map users, user groups, and roles
kubectl -n kube-system patch configmap aws-auth --patch-file k8s_ops/prod-infra/patch/aws-auth.yaml
```

## Install ArgoCD

### Launch ArgoCD

```shell
kubectl create namespace argocd
kubectl apply -n argocd -f https://raw.githubusercontent.com/argoproj/argo-cd/stable/manifests/install.yaml
kubectl apply -f prod-infra/crds/argocd-appproj-infra.yaml

# Retrieve passwords
kubectl get secret -n argocd argocd-initial-admin-secret -o json | jq ".data.password | @base64d"
```

### Patch ArgoCD for crossplane

```shell
# add following to configmap:
kubectl edit configmap argocd-cm -n argocd

# add following by adding data section
# https://github.com/argoproj/argo-cd/blob/fdcaa550ccf33d78838acd2dbc04f211682873a8/docs/operator-manual/argocd-cm.yaml#L192
#  data:
#    resource.compareoptions: |
#      ignoreAggregatedRoles: true
```

### ArgoCD operations

```shell
NEWPASS=<something>

# get argocd UI
UI=$(kubectl get svc -n argocd argocd-server -o jsonpath="{.status.loadBalancer.ingress[0].hostname}")

# get admin password
PASS=$(kubectl -n argocd get secret argocd-initial-admin-secret -o jsonpath="{.data.password}" | base64 -d)

# Patch node to be scheduled on ON_DEMAND node
kubectl -n argocd patch deployment argocd-server --patch-file k8s_ops/prod-infra/patch/argocd.yaml
kubectl -n argocd patch deployment argocd-repo-server --patch-file k8s_ops/prod-infra/patch/argocd.yaml
kubectl -n argocd patch deployment argocd-redis --patch-file k8s_ops/prod-infra/patch/argocd.yaml
kubectl -n argocd patch deployment argocd-notifications-controller --patch-file k8s_ops/prod-infra/patch/argocd.yaml
kubectl -n argocd patch deployment argocd-dex-server --patch-file k8s_ops/prod-infra/patch/argocd-dex.yaml
kubectl -n argocd patch deployment argocd-applicationset-controller --patch-file k8s_ops/prod-infra/patch/argocd.yaml

# reset admin password
argocd login  --username admin --password $PASS --grpc-web $UI --insecure
argocd account update-password --current-password $PASS --new-password $NEWPASS

# apply gitlab credential (optional)

# apply appproject (infra) & application (ns-argocd) to get started
```

### ArgoCD upgrade

```shell
# upgradeing argocd
kubectl apply -n argocd -f https://raw.githubusercontent.com/argoproj/argo-cd/stable/manifests/install.yaml
# updating dex file and patch
kubectl -n argocd patch deployment argocd-dex-server --patch-file k8s_ops/prod-infra/patch/argocd-dex.yaml
# Configure argocd oidc
kaf k8s_ops/prod-infra/crds/configmap-argocd.yaml
```

## Install crossplane

```shell
kaf k8s_ops/prod-infra/argocd/crossplane.yaml
# repeated execution required until success
kaf k8s_ops/prod-infra/crossplane/provider/provider-aws.yaml

# Add regional option
# kubectl annotate serviceaccount -n service-account-namespace service-account-name \
# eks.amazonaws.com/sts-regional-endpoints=true
# kubectl annotate serviceaccount -n crossplane-system crossplane eks.amazonaws.com/sts-regional-endpoints=true
```

## Install infra components

* Follow the steps: policy -> role -> app
* Updated: policy is now applied in terraform because policy is not working in China region

### aws-ebs-csi-driver

``` shell
# using aws managed ebs csi driver
kaf k8s_ops/prod-infra/crds/storageclass.yaml

# install iam role for csi driver
kaf k8s_ops/prod-infra/crossplane/infra/iam-role-ebs-csi-driver.yaml

# annotate service account
# kubectl annotate serviceaccount -n kube-system ebs-csi-controller-sa eks.amazonaws.com/role-arn=arn:aws-cn:iam::************:role/prod-infra-ebs-csi-driver

# !NOTE: restart pod ebs-csi-controller-* to make env "AWS_ROLE_ARN" etc injected to pod

# make ebs-sc the only default driver
kubectl patch storageclass gp2 -p '{"metadata": {"annotations":{"storageclass.kubernetes.io/is-default-class":"false"}}}'
```

### ingress-nginx

```shell
# install ingress-nginx
kaf k8s_ops/prod-infra/argocd/ingress-nginx-internal.yaml
kaf k8s_ops/prod-infra/argocd/ingress-nginx-external.yaml
```

### Route53

```shell
# install iampolicy for r53
kaf k8s_ops/prod-infra/crossplane/infra/iam-policy-route53.yaml

# install r53 zone for rp.konvery.work
# Normally, firstly create zone then update ns [and original zone id] (retrieve infos from r53 console)
# Note: for AWS China, konvery domains are managed by dnspod.
# However, still require a public zone for let's encrypt cert
kaf k8s_ops/prod-infra/crossplane/infra/r53-zone-rp-konvery-work-private.yaml
kaf k8s_ops/prod-infra/crossplane/infra/r53-zone-rp-konvery-work-public.yaml

# Manually associate non-prod-infra vpc with rp-konvery-work-private zone.
# This will enable openvpn dns
```

### External secret

```shell
# install iampolicy for external secret
kaf k8s_ops/prod-infra/crossplane/infra/iam-policy-secret-manager.yaml

# install role for external secret
# update line 16 and line 20 with EKS cluster id
kaf k8s_ops/prod-infra/crossplane/infra/iam-role-externalsecret.yaml

# install external secret app
# install external secret ClusterSecretStore
kaf k8s_ops/prod-infra/argocd/external-secret.yaml
```

### External dns

```shell
kaf k8s_ops/prod-infra/crossplane/infra/iam-role-externaldns.yaml
kaf k8s_ops/prod-infra/argocd/external-dns-internal.yaml
kaf k8s_ops/prod-infra/argocd/external-dns-external.yaml
```

### Certification

#### Cert Manager

``` shell
# Role
kaf k8s_ops/prod-infra/crossplane/infra/iam-role-certmanager.yaml

# AWS ACM Private CA is not available in China
kaf k8s_ops/prod-infra/argocd/cert-manager.yaml

## cluster issuer
kaf k8s_ops/prod-infra/crds/cert-clusterissuer-rp-konvery-work.yaml

## generate domain certificate
kaf k8s_ops/prod-infra/crds/cert-wildcard-rp-konvery-work.yaml

## Create argocd service endpoint
kaf k8s_ops/prod-infra/crds/ingress-argocd.yaml
```

## Middleware

### postgresql database

```shell
# install database customized sg
# * modify crossplane/infra/ec2-sg-postgresql.yaml
# * vpcid, groupid
kaf k8s_ops/prod-infra/crossplane/infra/ec2-sg-postgresql.yaml

# manually init db passwd
## To be changed by community, PR raised
kubectl create secret generic manual-initial-db-pw --from-literal=password=<passwd> -n crossplane-system

# install rds for all infra shared DB Cluster
kaf k8s_ops/prod-infra/crossplane/infra/rds-postgresql-dbcluster.yaml

# install rds instance as rw node in DB Cluster
kaf k8s_ops/prod-infra/crossplane/infra/rds-postgresql-dbinstance.yaml

# install crossplane sql provider
# repeated execution required until success
kaf k8s_ops/prod-infra/crossplane/provider/provider-postgresql.yaml
```

## Dev Apps

### Install KeyCloak

```shell
# create db role and corresponding providerconfig
kaf k8s_ops/prod-infra/crossplane/infra/postgresql-role-keycloak.yaml
kaf k8s_ops/prod-infra/crossplane/infra/postgresql-config-keycloak.yaml

# install db provisioning for keycloak with its role
kaf k8s_ops/prod-infra/crossplane/infra/postgresql-db-keycloak.yaml

# export secret of keycloak role to secret manager via crossplane
kaf k8s_ops/prod-infra/crossplane/infra/sec-postgresql-role-keycloak.yaml

# install k8s secret from secret manager via external secret (with re-format)
kaf k8s_ops/prod-infra/crds/extsec-postgresql-role-keycloak.yaml

# install keycloak app
kaf k8s_ops/prod-infra/argocd/keycloak.yaml

# retrieve default admin credential for verification
kubectl get secret credential-keycloak -o go-template='{{range $k,$v := .data}}{{printf "%s: " $k}}{{if not $v}}{{$v}}{{else}}{{$v | base64decode}}{{end}}{{"\n"}}{{end}}' -n infra
# reset password pls...

# retrieve open-id endpoint infos
https://keycloak.rp.konvery.work/realms/konvery/.well-known/openid-configuration

# https://keycloak.rp.konvery.work/auth/realms/konvery/protocol/openid-connect/auth
authorization_endpoint = Auth URL
# https://keycloak.rp.konvery.work/auth/realms/konvery/protocol/openid-connect/userinfo
userinfo_endpoint = API URL
# https://keycloak.rp.konvery.work/auth/realms/konvery/protocol/openid-connect/token
token_endpoint = Token URL
# artifactory
Client ID
```

### Install Artifactory

```shell
# iampolicy
kaf k8s_ops/prod-infra/crossplane/infra/iam-policy-artifactory.yaml

# artifactory role
kaf k8s_ops/prod-infra/crossplane/infra/iam-role-artifactory.yaml

# s3
kaf k8s_ops/prod-infra/crossplane/infra/s3-artifactory.yaml

# database
kaf k8s_ops/prod-infra/crossplane/infra/postgresql-role-artifactory.yaml
kaf k8s_ops/prod-infra/crossplane/infra/postgresql-config-artifactory.yaml
kaf k8s_ops/prod-infra/crossplane/infra/postgresql-db-artifactory.yaml

# db secret to aws secret manager
kaf k8s_ops/prod-infra/crossplane/infra/sec-postgresql-role-artifactory.yaml

# aws secret manager to external-secret
kaf k8s_ops/prod-infra/crds/extsec-postgresql-role-artifactory.yaml

# instance
kaf k8s_ops/prod-infra/argocd/artifactory.yaml

# install keycloak client
kaf k8s_ops/prod-infra/crds/keycloak-client-artifactory.yaml
```

Configure openid-connect by KeyCloak manually

|Key|Value|
|:-:|:-:|
|Provider Name|Keycloak|
|Provider Type|OpenID|
|Client ID|artifactory|
|Secret|keycloak->Clients->Artifactory->Credentials->Secret|
|Auth URL|openid-conf.json->authorization_endpoint|
|API URL|openid-conf.json->userinfo_endpoint|
|Token URL|openid-conf.json->token_endpoint|

### Install Gitlab

```shell
# label node for affinity
kubectl label nodes ip-10-64-160-101.cn-northwest-1.compute.internal release=gitlab

# s3
kaf k8s_ops/prod-infra/crossplane/infra/s3-gitlab.yaml

# iampolicy
kaf k8s_ops/prod-infra/crossplane/infra/iam-policy-gitlab.yaml

# role
kaf k8s_ops/prod-infra/crossplane/infra/iam-role-gitlab.yaml

# database
kaf k8s_ops/prod-infra/crossplane/infra/postgresql-role-gitlab.yaml
kaf k8s_ops/prod-infra/crossplane/infra/postgresql-config-gitlab.yaml
kaf k8s_ops/prod-infra/crossplane/infra/postgresql-db-gitlab.yaml

# install gitlab storage config
kaf k8s_ops/prod-infra/crds/secret-gitlab.yaml

# OIDC
kaf k8s_ops/prod-infra/crds/keycloak-client-gitlab.yaml
kaf k8s_ops/prod-infra/crossplane/infra/sec-keycloak-client-token-gitlab.yaml
kaf k8s_ops/prod-infra/crds/extsec-keycloak-auth-gitlab.yaml

# install gitlab app
kaf k8s_ops/prod-infra/argocd/gitlab.yaml

# default gitlab password
kubectl get secret gitlab-gitlab-initial-root-password -o go-template='{{range $k,$v := .data}}{{printf "%s: " $k}}{{if not $v}}{{$v}}{{else}}{{$v | base64decode}}{{end}}{{"\n"}}{{end}}' -n infra

# retrieve runner registration token
# install gitlab-runner app
kaf k8s_ops/prod-infra/argocd/gitlab-runner.yaml
```

### Configure Argocd with OpenID-Connector

```shell
# https://medium.com/geekculture/integrate-keycloak-and-argocd-within-kubernetes-with-ease-6871c620d3b3

# Add keyclock-client
kaf k8s_ops/prod-infra/crds/keycloak-client-argocd.yaml

# create group mapper on keyclock console

# update client credential
# Configure argocd oidc
kaf k8s_ops/prod-infra/crds/configmap-argocd.yaml
```

## Add datasource customer

```shell
# s3 bucket
kaf k8s_ops/prod-infra/crossplane/workload/s3-databot.yaml

# policy
kaf k8s_ops/prod-infra/crossplane/workload/iam-policy-databot.yaml

# user-role-binding
kaf k8s_ops/prod-infra/crossplane/workload/iam-user-databot.yaml
```

### Install Prometheus

Using deployment of prometheus operator

```shell
# upgrade begin
# clone repo for k8s v1.26
git clone https://github.com/prometheus-operator/kube-prometheus
git checkout release-0.13

# Create the namespace and CRDs, and then wait for them to be available before creating the remaining resources
# Note that due to some CRD size we are using kubectl server-side apply feature which is generally available since kubernetes 1.22.
# If you are using previous kubernetes versions this feature may not be available and you would need to use kubectl create instead.
kubectl apply --server-side -f manifests/setup
kubectl -n monitoring wait --for condition=Established --all CustomResourceDefinition
kubectl apply -f manifests/

# affinity context begin
kubectl -n monitoring patch alertmanager.monitoring.coreos.com/main --type merge --patch-file k8s_ops/prod-infra/patch/prometheus-alertmanager.yaml
## + persistentVolume
kubectl -n monitoring patch prometheus.monitoring.coreos.com/k8s --type merge --patch-file k8s_ops/prod-infra/patch/prometheus-k8s.yaml
kubectl -n monitoring patch deployment blackbox-exporter --patch-file k8s_ops/prod-infra/patch/prometheus-blackbox-exporter.yaml
kubectl -n monitoring patch deployment grafana --patch-file k8s_ops/prod-infra/patch/prometheus-grafana.yaml
## + container patch
kubectl -n monitoring patch deployment kube-state-metrics --patch-file k8s_ops/prod-infra/patch/prometheus-kube-state-metrics.yaml
## + container patch
kubectl -n monitoring patch deployment prometheus-adapter --patch-file k8s_ops/prod-infra/patch/prometheus-adapter.yaml
kubectl -n monitoring patch deployment prometheus-operator --patch-file k8s_ops/prod-infra/patch/prometheus-operator.yaml
# affinity context end

# patch role permissions
kubectl patch clusterrole/prometheus-k8s --patch-file k8s_ops/prod-infra/patch/prometheus-k8s-cluster-role.yaml

# delete all network policies
for i in $(kubectl -n monitoring get networkpolicy -o jsonpath='{.items[*].metadata.name}')
do
   kubectl -n monitoring delete networkpolicy $i
done
# upgrade + last patch

# add ingress
kaf k8s_ops/prod-infra/crds/ingress-prometheus-grafana.yaml

# add oidc for keycloak
kaf k8s_ops/prod-infra/crds/keycloak-client-grafana.yaml
# create role mapper in keycloak console
# manually add mapper in the client config (mapper type: User Realm Role, Token Claim Name: realm_access.roles, Claim JSON Type: String)
kubectl -n monitoring patch secret grafana-config --patch-file k8s_ops/prod-infra/patch/grafana.yaml
```

### Install Loki

```shell
# create pull secret
# refer to k8s_ops/prod-infra/crds/secret-artifactory-token.yaml

# create app
kaf k8s_ops/prod-infra/argocd/loki.yaml
```

### config cloudtrail for data event

```shell

k8s_ops/prod-infra/crossplane/workload/s3-cloudtrail-logs.yaml

```
