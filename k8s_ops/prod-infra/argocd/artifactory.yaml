apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: artifactory
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: infra
  destination:
    namespace: infra
    server: 'https://kubernetes.default.svc'
  source:
    repoURL: 'https://charts.jfrog.io'
    targetRevision: '107.55.10'
    chart: artifactory
    helm:
      values: |
        serviceAccount:
          create: true
          name: artifactory
          annotations:
            eks.amazonaws.com/role-arn: arn:aws-cn:iam::************:role/prod-artifactory
        ingress:
          enabled: true
          className: ingress-nginx-internal
          defaultBackend:
            enabled: true
          hosts:
            - artifactory.rp.konvery.work
          routerPath: /
          artifactoryPath: /artifactory/
          annotations:
            nginx.ingress.kubernetes.io/configuration-snippet: |
              proxy_pass_header   Server;
              proxy_set_header    X-JFrog-Override-Base-Url https://artifactory.rp.konvery.work;
            # kubernetes.io/tls-acme: "true"
            nginx.ingress.kubernetes.io/proxy-body-size: "0"
            external-dns-annotation: ingress-nginx-internal
          tls:
            - secretName: wildcard-rp-konvery-work
              hosts:
                - artifactory.rp.konvery.work
        frontend:
          session:
            timeoutMinutes: '240'
        artifactory:
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                    - key: eks.amazonaws.com/capacityType
                      operator: In
                      values:
                      - ON_DEMAND
          database:
            maxOpenConnections: 100
          openMetrics:
            enabled: true
          persistence:
            size: 200Gi
            type: aws-s3-v3
            awsS3V3:
              region: cn-northwest-1
              bucketName: prod-infra-artifactory
              endpoint: s3.cn-northwest-1.amazonaws.com.cn
              useInstanceCredentials: true
            storageClassName: "ebs-sc"
          resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1"
        nginx:
          enabled: false
        waitForDatabase: false
        postgresql:
          enabled: false
        database:
          type: postgresql
          driver: org.postgresql.Driver
          secrets:
            user:
              name: "artifactory-db-secret"
              key: "username"
            password:
              name: "artifactory-db-secret"
              key: "password"
            url:
              name: "artifactory-db-secret"
              key: "url"
        probes:
          timeoutSeconds: 600
  syncPolicy:
    automated:
      selfHeal: true
      prune: true
    syncOptions:
    - CreateNamespace=true