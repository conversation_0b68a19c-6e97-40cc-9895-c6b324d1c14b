---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: aws-cert-sync
  namespace: argocd
spec:
  destination:
    namespace: infra
    server: https://kubernetes.default.svc
  project: infra
  source:
    chart: aws-cert-sync
    helm:
      values: |
        serviceAccount:
          annotations:
            eks.amazonaws.com/role-arn: arn:aws-cn:iam::************:role/prod-aws-cert-sync
        job:
          # Sunday morning 4am
          schedule: "0 4 * * 0"
          command: ["/bin/aws-cert-sync.sh"]
    repoURL: https://artifactory.rp.konvery.work/artifactory/helm
    targetRevision: 1.1.0
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
