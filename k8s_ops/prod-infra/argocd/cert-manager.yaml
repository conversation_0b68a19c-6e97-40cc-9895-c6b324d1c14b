apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: cert-manager
  namespace: argocd
spec:
  destination:
    namespace: infra
    server: https://kubernetes.default.svc
  project: infra
  source:
    chart: cert-manager
    helm:
      values: |
        cainjector:
          image:
            repository: quay.m.daocloud.io/jetstack/cert-manager-cainjector
        extraArgs:
        - --dns01-recursive-nameservers-only
        - --dns01-recursive-nameservers=119.29.29.29:53,8.8.8.8:53
        image:
          repository: quay.m.daocloud.io/jetstack/cert-manager-controller
        installCRDs: true
        serviceAccount:
          annotations:
            eks.amazonaws.com/role-arn: arn:aws-cn:iam::************:role/prod-certmanager
        startupapicheck:
          image:
            repository: quay.m.daocloud.io/jetstack/cert-manager-ctl
        webhook:
          image:
            repository: quay.m.daocloud.io/jetstack/cert-manager-webhook
    repoURL: https://charts.jetstack.io
    targetRevision: v1.11.0
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
