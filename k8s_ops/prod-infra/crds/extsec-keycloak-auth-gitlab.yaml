---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: gitlab-omniauth-keycloak-provider
  namespace: infra
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: external-secrets
    kind: ClusterSecretStore
  target:
    name: gitlab-omniauth-keycloak-provider
    creationPolicy: Owner
    template: 
      data:
        provider: |
          { 
            name: 'openid_connect',
            label: 'Keycloak',
            args: {
              name: 'openid_connect',
              scope: ['openid','email','profile'],
              response_type: 'code',
              issuer: 'https://keycloak.rp.konvery.work/realms/konvery',
              discovery: true,
              client_auth_method: 'query',
              uid_field: 'preferred_username',
              send_scope_to_token_endpoint: false,
              client_options: {
                identifier: "{{ .CLIENT_ID | toString }}",
                secret: "{{ .CLIENT_SECRET | toString }}",
                redirect_uri: 'https://gitlab.rp.konvery.work/users/auth/openid_connect/callback'
              }
            }
          }
  dataFrom:
    - extract: 
        key: prod-keycloak-client-credential-gitlab
