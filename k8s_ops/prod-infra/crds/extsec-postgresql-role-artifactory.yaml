apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: artifactory-db-secret
  namespace: infra
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: external-secrets
    kind: ClusterSecretStore
  target:
    name: artifactory-db-secret
    creationPolicy: Owner
    template: 
      data:
        url: "jdbc:postgresql://{{ .endpoint | toString }}:5432/artifactory"
        password: "{{ .password | toString }}"
        username: "{{ .username | toString }}"
  dataFrom:
    - extract:
        key: prod-infra-postgresql-role-artifactory
