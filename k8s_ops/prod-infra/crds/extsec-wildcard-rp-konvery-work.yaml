---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: wildcard-rp-konvery-work
  namespace: argocd
spec:
  dataFrom:
    - extract:
        key: wildcard-rp-konvery-work
  refreshInterval: 1h
  secretStoreRef:
    kind: ClusterSecretStore
    name: external-secrets
  target:
    creationPolicy: Owner
    name: wildcard-rp-konvery-work
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: wildcard-rp-konvery-work
  namespace: monitoring
spec:
  dataFrom:
    - extract:
        key: wildcard-rp-konvery-work
  refreshInterval: 1h
  secretStoreRef:
    kind: ClusterSecretStore
    name: external-secrets
  target:
    creationPolicy: Owner
    name: wildcard-rp-konvery-work
