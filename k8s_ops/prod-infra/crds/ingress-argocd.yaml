apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    # kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/backend-protocol: HTTPS
    nginx.ingress.kubernetes.io/ssl-passthrough: "true"
    external-dns-annotation: ingress-nginx-internal
  name: argocd
  namespace: argocd
spec:
  ingressClassName: ingress-nginx-internal
  rules:
  - host: argocd.rp.konvery.work
    http:
      paths:
      - backend:
          service: 
            name: argocd-server
            port: 
              name: https
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - argocd.rp.konvery.work
    secretName: wildcard-rp-konvery-work