---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    external-dns-annotation: ingress-nginx-internal
    kubernetes.io/ingress.class: ingress-nginx-internal
    nginx.ingress.kubernetes.io/backend-protocol: HTTPS
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/server-snippet: |2-
                            location ~* "^/auth/realms/master/metrics" {
                                return 301 /auth/realms/master;
                              }
  labels:
    app: keycloak
  name: keycloak
  namespace: infra
spec:
  # ingressClassName: ingress-nginx-internal
  rules:
  - host: keycloak.rp.konvery.work
    http:
      paths:
      - backend:
          service:
            name: keycloak
            port:
              number: 8443
        path: /
        pathType: ImplementationSpecific
  tls:
    - hosts:
      - keycloak.rp.konvery.work
      secretName: wildcard-rp-konvery-work
