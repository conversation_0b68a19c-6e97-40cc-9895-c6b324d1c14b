---
apiVersion: keycloak.org/v1alpha1
kind: KeycloakClient
metadata:
  name: argocd-workload-np
  namespace: infra
  labels:
    app: keycloak
spec:
  client:
    clientId: argocd-workload-np
    name: argocd-workload-np
    protocol: openid-connect
    standardFlowEnabled: true
    directAccessGrantsEnabled: true
    defaultClientScopes:
      - "email"
      - "profile"
      - "roles"
      - "web-origins"
      - "groups"
    optionalClientScopes:
      - "address"
      - "microprofile-jwt"
      - "offline_access"
      - "phone"
    adminUrl: https://argocd-workload.np.konvery.work/
    webOrigins:
      - "https://argocd-workload.np.konvery.work"
    rootUrl: https://argocd-workload.np.konvery.work/
    redirectUris:
      - "https://argocd-workload.np.konvery.work/auth/callback"
    baseUrl: /applications
  realmSelector:
    matchLabels:
      app: keycloak
