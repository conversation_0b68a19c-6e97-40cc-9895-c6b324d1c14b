---
apiVersion: keycloak.org/v1alpha1
kind: KeycloakClient
metadata:
  name: argocd-workload-rp
  namespace: infra
  labels:
    app: keycloak
spec:
  client:
    clientId: argocd-workload-rp
    name: argocd-workload-rp
    protocol: openid-connect
    standardFlowEnabled: true
    directAccessGrantsEnabled: true
    defaultClientScopes:
      - "email"
      - "profile"
      - "roles"
      - "web-origins"
      - "groups"
    optionalClientScopes:
      - "address"
      - "microprofile-jwt"
      - "offline_access"
      - "phone"
    adminUrl: https://argocd-workload.rp.konvery.work/
    webOrigins:
      - "https://argocd-workload.rp.konvery.work"
    rootUrl: https://argocd-workload.rp.konvery.work/
    redirectUris:
      - "https://argocd-workload.rp.konvery.work/auth/callback"
    baseUrl: /applications
  realmSelector:
    matchLabels:
      app: keycloak
