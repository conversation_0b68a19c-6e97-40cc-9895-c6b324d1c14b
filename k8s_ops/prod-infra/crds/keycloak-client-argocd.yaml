---
apiVersion: keycloak.org/v1alpha1
kind: KeycloakClient
metadata:
  name: argocd
  namespace: infra
  labels:
    app: keycloak
spec:
  client:
    clientId: argocd
    name: argocd
    protocol: openid-connect
    standardFlowEnabled: true
    directAccessGrantsEnabled: true
    defaultClientScopes:
      - "email"
      - "profile"
      - "roles"
      - "web-origins"
      - "groups"
    optionalClientScopes:
      - "address"
      - "microprofile-jwt"
      - "offline_access"
      - "phone"
    adminUrl: https://argocd.rp.konvery.work/
    webOrigins:
      - "https://argocd.rp.konvery.work"
    rootUrl: https://argocd.rp.konvery.work/
    redirectUris:
      - "https://argocd.rp.konvery.work/auth/callback"
    baseUrl: /applications
  realmSelector:
    matchLabels:
      app: keycloak
