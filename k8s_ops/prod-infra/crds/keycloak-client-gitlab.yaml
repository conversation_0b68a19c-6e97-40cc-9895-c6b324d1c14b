---
apiVersion: keycloak.org/v1alpha1
kind: KeycloakClient
metadata:
  name: gitlab
  namespace: infra
  labels:
    app: keycloak
spec:
  client:
    clientId: gitlab
    name: gitlab
    standardFlowEnabled: true
    directAccessGrantsEnabled: true
    defaultClientScopes:
      - "email"
      - "profile"
      - "roles"
      - "web-origins"
    optionalClientScopes:
      - "address"
      - "microprofile-jwt"
      - "offline_access"
      - "phone"
    adminUrl: https://gitlab.rp.konvery.work/
    webOrigins:
      - "https://gitlab.rp.konvery.work"
    rootUrl: https://gitlab.rp.konvery.work/
    redirectUris:
      - "https://gitlab.rp.konvery.work/*"
  realmSelector:
    matchLabels:
      app: keycloak
