---
apiVersion: keycloak.org/v1alpha1
kind: KeycloakClient
metadata:
  name: grafana-workload-np
  namespace: infra
  labels:
    app: keycloak
spec:
  client:
    clientId: grafana-workload-np
    name: grafana-workload-np
    protocol: openid-connect
    standardFlowEnabled: true
    directAccessGrantsEnabled: true
    defaultClientScopes:
      - "email"
      - "profile"
      - "roles"
      - "web-origins"
    optionalClientScopes:
      - "address"
      - "microprofile-jwt"
      - "offline_access"
      - "phone"
    adminUrl: https://grafana-workload.np.konvery.work/
    webOrigins:
      - "https://grafana-workload.np.konvery.work"
    rootUrl: https://grafana-workload.np.konvery.work/
    redirectUris:
      - "https://grafana-workload.np.konvery.work/login/*"
    # baseUrl: /applications
  realmSelector:
    matchLabels:
      app: keycloak
