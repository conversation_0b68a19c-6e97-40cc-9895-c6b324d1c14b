---
apiVersion: keycloak.org/v1alpha1
kind: KeycloakClient
metadata:
  name: grafana-workload-rp
  namespace: infra
  labels:
    app: keycloak
spec:
  client:
    clientId: grafana-workload-rp
    name: grafana-workload-rp
    protocol: openid-connect
    standardFlowEnabled: true
    directAccessGrantsEnabled: true
    defaultClientScopes:
      - "email"
      - "profile"
      - "roles"
      - "web-origins"
    optionalClientScopes:
      - "address"
      - "microprofile-jwt"
      - "offline_access"
      - "phone"
    adminUrl: https://grafana-workload.rp.konvery.work/
    webOrigins:
      - "https://grafana-workload.rp.konvery.work"
    rootUrl: https://grafana-workload.rp.konvery.work/
    redirectUris:
      - "https://grafana-workload.rp.konvery.work/login/*"
    # baseUrl: /applications
  realmSelector:
    matchLabels:
      app: keycloak
