---
apiVersion: keycloak.org/v1alpha1
kind: KeycloakClient
metadata:
  labels:
    app: keycloak
  name: grafana-rp
  namespace: infra
spec:
  client:
    adminUrl: https://grafana.rp.konvery.work/
    clientId: grafana-rp
    defaultClientScopes:
      - email
      - profile
      - roles
      - web-origins
    directAccessGrantsEnabled: true
    name: grafana-rp
    optionalClientScopes:
      - address
      - microprofile-jwt
      - offline_access
      - phone
    protocol: openid-connect
    redirectUris:
      - https://grafana.rp.konvery.work/login/*
    rootUrl: https://grafana.rp.konvery.work/
    standardFlowEnabled: true
    webOrigins:
      - https://grafana.rp.konvery.work
    # baseUrl: /applications
  realmSelector:
    matchLabels:
      app: keycloak