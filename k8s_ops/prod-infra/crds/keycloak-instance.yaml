apiVersion: keycloak.org/v1alpha1
kind: Keycloak
metadata:
  labels:
    app: keycloak
  name: keycloak
  namespace: infra
spec:
  externalAccess:
    enabled: true
    host: keycloak.rp.konvery.work
  externalDatabase:
    enabled: true
  instances: 2
  keycloakDeploymentSpec:
    experimental:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                - key: eks.amazonaws.com/capacityType
                  operator: In
                  values:
                  - ON_DEMAND
