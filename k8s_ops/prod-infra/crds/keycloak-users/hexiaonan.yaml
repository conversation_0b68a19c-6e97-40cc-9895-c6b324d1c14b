---
apiVersion: keycloak.org/v1alpha1
kind: KeycloakUser
metadata:
  labels:
    app: keycloak
  name: keycloak-user-hexiaonan
  namespace: infra
spec:
  realmSelector:
    matchLabels:
      app: keycloak
  user:
    credentials:
      - temporary: false
        type: password
        value: aRROv1R5cK6rQt9V
    email: <EMAIL>
    enabled: true
    firstName: <PERSON>nan
    lastName: He
    groups:
    # admin, editor or viewer
      - kvy-admin
    username: he<PERSON><PERSON>n
