---
apiVersion: keycloak.org/v1alpha1
kind: KeycloakUser
metadata:
  labels:
    app: keycloak
  name: keycloak-user-maxiyan
  namespace: infra
spec:
  realmSelector:
    matchLabels:
      app: keycloak
  user:
    credentials:
      - temporary: false
        type: password
        value: GGc3eIsCXcJmS57e
    email: <EMAIL>
    enabled: true
    firstName: <PERSON>yan
    lastName: Ma
    groups:
    # admin, editor or viewer
      - kvy-editor
    username: maxiyan
