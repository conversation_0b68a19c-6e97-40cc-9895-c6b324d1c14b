apiVersion: ec2.aws.crossplane.io/v1beta1
kind: SecurityGroup
metadata: 
  name: prod-infra-postgresql-sg
spec:
  deletionPolicy: Delete
  forProvider:
    description: Security group for database in prod infra vpc
    groupName: prod-infra-postgresql-sg
    region: cn-northwest-1
    ingress: 
      - fromPort: 5432
        toPort: 5432
        ipProtocol: TCP
        userIdGroupPairs:
          - groupId: "sg-041158bd730fda0fb"
            description: "Allow all eks workers to access database"
          - groupId: "sg-079f6e26c63f9d974"
            description: "Allow prod-infra-new instance to access database"
    vpcId: vpc-0a0b50e614347e89f # prod-infra vpc id
    tags:
      - key: Name
        value: prod-infra-postgresql-sg
      - key: owner
        value: sre
      - key: profile
        value: prod
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
