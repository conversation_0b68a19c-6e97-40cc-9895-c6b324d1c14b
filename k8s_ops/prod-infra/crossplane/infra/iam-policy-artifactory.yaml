apiVersion: iam.aws.crossplane.io/v1beta1
kind: Policy
metadata:
  name: prod-artifactory
spec:
  deletionPolicy: Delete
  forProvider:
    document: |
      {
          "Statement": [
              {
                  "Action": [
                      "s3:DeleteObject",
                      "s3:DeleteObjectVersion",
                      "s3:GetObject",
                      "s3:ListMultipartUploadParts",
                      "s3:PutObject"
                  ],
                  "Effect": "Allow",
                  "Resource": "arn:aws-cn:s3:::prod-infra-artifactory/*",
                  "Sid": "objectPermission"
              },
              {
                  "Action": [
                      "s3:ListAllMyBuckets",
                      "s3:ListJobs",
                      "s3:ListStorageLensConfigurations"
                  ],
                  "Effect": "Allow",
                  "Resource": "*",
                  "Sid": "allPermission"
              },
              {
                  "Action": [
                      "s3:ListBucket",
                      "s3:ListBucketMultipartUploads",
                      "s3:ListBucketVersions"
                  ],
                  "Effect": "Allow",
                  "Resource": "arn:aws-cn:s3:::prod-infra-artifactory",
                  "Sid": "bucketPermission"
              }
          ],
          "Version": "2012-10-17"
      }
    name: prod-artifactory
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
