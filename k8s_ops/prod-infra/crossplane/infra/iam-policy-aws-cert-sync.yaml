apiVersion: iam.aws.crossplane.io/v1beta1
kind: Policy
metadata:
  name: prod-aws-cert-sync
spec:
  deletionPolicy: Delete
  forProvider:
    document: |
      {
          "Statement": [
              {
                  "Action": [
                      "iam:UpdateServerCertificate",
                      "iam:UploadServerCertificate",
                      "iam:TagServerCertificate",
                      "iam:DeleteServerCertificate",
                      "iam:ListServerCertificates"
                  ],
                  "Effect": "Allow",
                  "Resource": "*"
              }
          ],
          "Version": "2012-10-17"
      }
    name: prod-aws-cert-sync
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
