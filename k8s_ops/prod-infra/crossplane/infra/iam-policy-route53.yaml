apiVersion: iam.aws.crossplane.io/v1beta1
kind: Policy
metadata:
  name: prod-route53
spec:
  deletionPolicy: Delete
  forProvider:
    document: |
      {
          "Statement": [
              {
                  "Action": "route53:GetChange",
                  "Effect": "Allow",
                  "Resource": "arn:aws-cn:route53:::change/*"
              },
              {
                  "Action": [
                      "route53:ChangeResourceRecordSets",
                      "route53:ListResourceRecordSets"
                  ],
                  "Effect": "Allow",
                  "Resource": "arn:aws-cn:route53:::hostedzone/*"
              },
              {
                  "Action": [
                      "route53:GetHostedZone",
                      "route53:GetHostedZoneCount",
                      "route53:ListHostedZones",
                      "route53:ListHostedZonesByName",
                      "route53:ListHostedZonesByVPC",
                      "route53:ListResourceRecordSets"
                  ],
                  "Effect": "Allow",
                  "Resource": "*"
              }
          ],
          "Version": "2012-10-17"
      }
    name: prod-route53
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
