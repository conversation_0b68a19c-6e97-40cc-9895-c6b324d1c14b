apiVersion: iam.aws.crossplane.io/v1beta1
kind: Role
metadata:
  name: prod-externalsecret
spec:
  deletionPolicy: Delete
  forProvider:
    assumeRolePolicyDocument: |
      {
        "Version": "2012-10-17",
        "Statement": [
          {
            "Effect": "Allow",
            "Action": "sts:AssumeRoleWithWebIdentity",
            "Principal": {
              "Federated": "arn:aws-cn:iam::************:oidc-provider/oidc.eks.cn-northwest-1.amazonaws.com.cn/id/D992B10663F789CF922194F3931BC851"
            },
            "Condition": {
              "StringEquals": {
                "oidc.eks.cn-northwest-1.amazonaws.com.cn/id/D992B10663F789CF922194F3931BC851:sub": "system:serviceaccount:infra:external-secrets"
              }
            }
          }
        ]
      }
    description: external-secrets to be used in prod-infra eks cluster
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: iam.aws.crossplane.io/v1beta1
kind: RolePolicyAttachment
metadata: 
  name: prod-externalsecret
spec: 
  deletionPolicy: Delete
  forProvider:
    roleNameRef:
      name: prod-externalsecret
    policyArn: arn:aws-cn:iam::************:policy/prod-secret-manager
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
