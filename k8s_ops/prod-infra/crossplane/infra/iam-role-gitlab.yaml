apiVersion: iam.aws.crossplane.io/v1beta1
kind: Role
metadata:
  name: prod-gitlab
spec:
  deletionPolicy: Delete
  forProvider:
    assumeRolePolicyDocument: |
      {
        "Version": "2012-10-17",
        "Statement": [
          {
            "Effect": "Allow",
            "Action": "sts:AssumeRoleWithWebIdentity",
            "Principal": {
              "Federated": "arn:aws-cn:iam::************:oidc-provider/oidc.eks.cn-northwest-1.amazonaws.com.cn/id/D992B10663F789CF922194F3931BC851"
            },
            "Condition" : {
              "StringLike" : {
                "oidc.eks.cn-northwest-1.amazonaws.com.cn/id/D992B10663F789CF922194F3931BC851:sub" : "system:serviceaccount:infra:gitlab*"
              }
            }
          }
        ]
      }
    description: gitlab role to be used in prod-infra eks cluster
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: iam.aws.crossplane.io/v1beta1
kind: RolePolicyAttachment
metadata: 
  name: prod-gitlab
spec: 
  deletionPolicy: Delete
  forProvider:
    roleNameRef:
      name: prod-gitlab
    policyArn: arn:aws-cn:iam::************:policy/prod-gitlab
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
