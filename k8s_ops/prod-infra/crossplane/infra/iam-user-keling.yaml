apiVersion: iam.aws.crossplane.io/v1beta1
kind: User
metadata:
  name: keling
spec:
  deletionPolicy: Delete
  forProvider:
    tags:
      - key: Name
        value: Ling Ke
      - key: Team
        value: SRE
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: iam.aws.crossplane.io/v1beta1
kind: AccessKey
metadata:
  name: keling
spec:
  forProvider:
    userNameRef:
      name: keling
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
  writeConnectionSecretToRef:
    name: iam-credential-keling
    namespace: crossplane-system
