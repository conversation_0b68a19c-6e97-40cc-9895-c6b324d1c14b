apiVersion: iam.aws.crossplane.io/v1beta1
kind: User
metadata:
  name: shengchaohua
spec:
  deletionPolicy: Delete
  forProvider:
    tags:
      - key: Name
        value: <PERSON><PERSON>g
      - key: Team
        value: Back-end
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: iam.aws.crossplane.io/v1beta1
kind: AccessKey
metadata:
  name: shengchaohua
spec:
  forProvider:
    userNameRef:
      name: shengchaohua
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
  writeConnectionSecretToRef:
    name: iam-credential-shengchaohua
    namespace: crossplane-system
