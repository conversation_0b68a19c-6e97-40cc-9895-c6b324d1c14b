apiVersion: iam.aws.crossplane.io/v1beta1
kind: User
metadata:
  name: wangqiushi
spec:
  deletionPolicy: Delete
  forProvider:
    tags:
      - key: Name
        value: <PERSON><PERSON><PERSON>
      - key: Team
        value: SRE
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: iam.aws.crossplane.io/v1beta1
kind: AccessKey
metadata:
  name: wangqiushi
spec:
  forProvider:
    userNameRef:
      name: wangqiushi
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
  writeConnectionSecretToRef:
    name: iam-credential-wangqiushi
    namespace: crossplane-system
