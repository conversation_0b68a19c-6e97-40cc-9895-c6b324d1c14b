---
apiVersion: postgresql.sql.crossplane.io/v1alpha1
kind: Role
metadata:
  name: keycloak
spec:
  deletionPolicy: Delete
  forProvider:
    connectionLimit: 10
    # passwordSecretRef:
    #   key: POSTGRES_PASSWORD
    #   name: keycloak-db-secret
    #   namespace: infra
    privileges:
      login: true
      createDb: true
      # inherit: true
  providerConfigRef:
    name: prod-infra-crossplane-provider-postgresql-config
  writeConnectionSecretToRef:
    name: keycloak-role-secret
    namespace: infra
