apiVersion: route53.aws.crossplane.io/v1alpha1
kind: HostedZone
metadata: 
  name: rp-konvery-work
spec: 
  deletionPolicy: Delete
  forProvider:
    name: rp.konvery.work
    config:
      privateZone: true
    vpc:
      vpcId: vpc-0a0b50e614347e89f
      # vpcIdSelector:
      #   matchLabels:
      #     name: non-prod-infra
      vpcRegion: cn-northwest-1
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
