---
apiVersion: rds.aws.crossplane.io/v1alpha1
kind: DBInstance
metadata:
  name: prod-infra-postgresql-instance
spec:
  forProvider:
    autoMinorVersionUpgrade: true
    dbClusterIdentifier: prod-infra-postgresql
    dbInstanceClass: db.t4g.medium
    dbParameterGroupName: default.aurora-postgresql14
    dbSubnetGroupName: prod-infra
    enablePerformanceInsights: true
    engine: aurora-postgresql
    engineVersion: "14.5"
    licenseModel: postgresql-license
    promotionTier: 1
    publiclyAccessible: false
    region: cn-northwest-1
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: rds.aws.crossplane.io/v1alpha1
kind: DBInstance
metadata:
  name: prod-infra-postgresql-instance-replica
spec:
  forProvider:
    autoMinorVersionUpgrade: true
    dbClusterIdentifier: prod-infra-postgresql
    dbInstanceClass: db.t4g.medium
    dbParameterGroupName: default.aurora-postgresql14
    dbSubnetGroupName: prod-infra
    enablePerformanceInsights: true
    engine: aurora-postgresql
    engineVersion: "14.5"
    licenseModel: postgresql-license
    promotionTier: 2
    publiclyAccessible: false
    region: cn-northwest-1
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config