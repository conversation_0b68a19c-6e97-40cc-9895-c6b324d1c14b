---
apiVersion: s3.aws.crossplane.io/v1beta1
kind: Bucket
metadata:
  name: prod-infra-gitlab-registry
spec:
  deletionPolicy: Orphan
  forProvider:
    acl: private
    locationConstraint: cn-northwest-1
    publicAccessBlockConfiguration:
      blockPublicAcls: true
      blockPublicPolicy: true
      ignorePublicAcls: true
      restrictPublicBuckets: true
    serverSideEncryptionConfiguration:
      rules:
        - applyServerSideEncryptionByDefault:
            sseAlgorithm: AES256
    tagging:
      tagSet:
        - key: owner
          value: konvery-sre
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: s3.aws.crossplane.io/v1beta1
kind: Bucket
metadata:
  name: prod-infra-gitlab-lfs
spec:
  deletionPolicy: Orphan
  forProvider:
    acl: private
    locationConstraint: cn-northwest-1
    publicAccessBlockConfiguration:
      blockPublicAcls: true
      blockPublicPolicy: true
      ignorePublicAcls: true
      restrictPublicBuckets: true
    serverSideEncryptionConfiguration:
      rules:
        - applyServerSideEncryptionByDefault:
            sseAlgorithm: AES256
    tagging:
      tagSet:
        - key: owner
          value: konvery-sre
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: s3.aws.crossplane.io/v1beta1
kind: Bucket
metadata:
  name: prod-infra-gitlab-artifacts
spec:
  deletionPolicy: Orphan
  forProvider:
    acl: private
    locationConstraint: cn-northwest-1
    publicAccessBlockConfiguration:
      blockPublicAcls: true
      blockPublicPolicy: true
      ignorePublicAcls: true
      restrictPublicBuckets: true
    serverSideEncryptionConfiguration:
      rules:
        - applyServerSideEncryptionByDefault:
            sseAlgorithm: AES256
    tagging:
      tagSet:
        - key: owner
          value: konvery-sre
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: s3.aws.crossplane.io/v1beta1
kind: Bucket
metadata:
  name: prod-infra-gitlab-uploads
spec:
  deletionPolicy: Orphan
  forProvider:
    acl: private
    locationConstraint: cn-northwest-1
    publicAccessBlockConfiguration:
      blockPublicAcls: true
      blockPublicPolicy: true
      ignorePublicAcls: true
      restrictPublicBuckets: true
    serverSideEncryptionConfiguration:
      rules:
        - applyServerSideEncryptionByDefault:
            sseAlgorithm: AES256
    tagging:
      tagSet:
        - key: owner
          value: konvery-sre
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: s3.aws.crossplane.io/v1beta1
kind: Bucket
metadata:
  name: prod-infra-gitlab-packages
spec:
  deletionPolicy: Orphan
  forProvider:
    acl: private
    locationConstraint: cn-northwest-1
    publicAccessBlockConfiguration:
      blockPublicAcls: true
      blockPublicPolicy: true
      ignorePublicAcls: true
      restrictPublicBuckets: true
    serverSideEncryptionConfiguration:
      rules:
        - applyServerSideEncryptionByDefault:
            sseAlgorithm: AES256
    tagging:
      tagSet:
        - key: owner
          value: konvery-sre
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: s3.aws.crossplane.io/v1beta1
kind: Bucket
metadata:
  name: prod-infra-gitlab-backup
spec:
  deletionPolicy: Orphan
  forProvider:
    acl: private
    locationConstraint: cn-northwest-1
    publicAccessBlockConfiguration:
      blockPublicAcls: true
      blockPublicPolicy: true
      ignorePublicAcls: true
      restrictPublicBuckets: true
    serverSideEncryptionConfiguration:
      rules:
        - applyServerSideEncryptionByDefault:
            sseAlgorithm: AES256
    tagging:
      tagSet:
        - key: owner
          value: konvery-sre
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: s3.aws.crossplane.io/v1beta1
kind: Bucket
metadata:
  name: prod-infra-gitlab-tmp
spec:
  deletionPolicy: Orphan
  forProvider:
    acl: private
    locationConstraint: cn-northwest-1
    publicAccessBlockConfiguration:
      blockPublicAcls: true
      blockPublicPolicy: true
      ignorePublicAcls: true
      restrictPublicBuckets: true
    serverSideEncryptionConfiguration:
      rules:
        - applyServerSideEncryptionByDefault:
            sseAlgorithm: AES256
    tagging:
      tagSet:
        - key: owner
          value: konvery-sre
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: s3.aws.crossplane.io/v1beta1
kind: Bucket
metadata:
  name: prod-infra-gitlab
spec:
  deletionPolicy: Orphan
  forProvider:
    acl: private
    locationConstraint: cn-northwest-1
    publicAccessBlockConfiguration:
      blockPublicAcls: true
      blockPublicPolicy: true
      ignorePublicAcls: true
      restrictPublicBuckets: true
    serverSideEncryptionConfiguration:
      rules:
        - applyServerSideEncryptionByDefault:
            sseAlgorithm: AES256
    tagging:
      tagSet:
        - key: owner
          value: konvery-sre
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
