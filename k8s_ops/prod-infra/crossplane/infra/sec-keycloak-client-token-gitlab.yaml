apiVersion: secretsmanager.aws.crossplane.io/v1beta1
kind: Secret
metadata:
  name: prod-keycloak-client-credential-gitlab
spec:
  forProvider:
    region: cn-northwest-1
    description: "prod KeyCloak gitlab client token"
    forceDeleteWithoutRecovery: true
    #recoveryWindowInDays: 7
    stringSecretRef:
      name: keycloak-client-secret-gitlab
      namespace: infra
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
