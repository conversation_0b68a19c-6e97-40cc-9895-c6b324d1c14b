---
apiVersion: apigatewayv2.aws.crossplane.io/v1alpha1
kind: API
metadata:
  name: awslark
spec:
  forProvider:
    description: lark audit to aws ec2 launch
    name: awslark
    protocolType: HTTP
    region: cn-northwest-1
    routeSelectionExpression: ${request.method} ${request.path}
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: apigatewayv2.aws.crossplane.io/v1alpha1
kind: Integration
metadata:
  name: awslark
spec:
  forProvider:
    apiIdRef:
      name: awslark
    integrationType: AWS_PROXY
    integrationURI: arn:aws-cn:lambda:cn-northwest-1:035532479701:function:awslark
    payloadFormatVersion: "2.0"
    region: cn-northwest-1
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: apigatewayv2.aws.crossplane.io/v1alpha1
kind: Route
metadata:
  name: awslark
spec:
  forProvider:
    apiIdRef:
      name: awslark
    region: cn-northwest-1
    routeKey: ANY /awslark
    targetRef:
      name: awslark
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: apigatewayv2.aws.crossplane.io/v1alpha1
kind: Stage
metadata:
  name: awslark-prod
spec:
  forProvider:
    apiIdRef:
      name: awslark
    region: cn-northwest-1
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: apigatewayv2.aws.crossplane.io/v1alpha1
kind: Deployment
metadata:
  name: awslark
spec:
  forProvider:
    apiIdRef:
      name: awslark
    region: cn-northwest-1
    stageNameRef:
      name: awslark-prod
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
