---
apiVersion: apigatewayv2.aws.crossplane.io/v1alpha1
kind: API
metadata:
  name: bitbot
spec:
  forProvider:
    description: bitable status automation
    name: bitbot
    protocolType: HTTP
    region: cn-northwest-1
    routeSelectionExpression: ${request.method} ${request.path}
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: apigatewayv2.aws.crossplane.io/v1alpha1
kind: Integration
metadata:
  name: bitbot
spec:
  forProvider:
    apiIdRef:
      name: bitbot
    integrationType: AWS_PROXY
    integrationURI: arn:aws-cn:lambda:cn-northwest-1:035532479701:function:bitbot
    payloadFormatVersion: "2.0"
    region: cn-northwest-1
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: apigatewayv2.aws.crossplane.io/v1alpha1
kind: Route
metadata:
  name: bitbot
spec:
  forProvider:
    apiIdRef:
      name: bitbot
    region: cn-northwest-1
    routeKey: ANY /bitbot
    targetRef:
      name: bitbot
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: apigatewayv2.aws.crossplane.io/v1alpha1
kind: Stage
metadata:
  name: bitbot-prod
spec:
  forProvider:
    apiIdRef:
      name: bitbot
    region: cn-northwest-1
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: apigatewayv2.aws.crossplane.io/v1alpha1
kind: Deployment
metadata:
  name: bitbot
spec:
  forProvider:
    apiIdRef:
      name: bitbot
    region: cn-northwest-1
    stageNameRef:
      name: bitbot-prod
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
