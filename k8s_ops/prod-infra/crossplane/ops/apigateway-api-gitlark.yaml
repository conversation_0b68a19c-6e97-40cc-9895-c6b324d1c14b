---
apiVersion: apigatewayv2.aws.crossplane.io/v1alpha1
kind: API
metadata:
  name: gitlark
spec:
  forProvider:
    description: git merge request to lark message automation
    name: gitlark
    protocolType: HTTP
    region: cn-northwest-1
    routeSelectionExpression: ${request.method} ${request.path}
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: apigatewayv2.aws.crossplane.io/v1alpha1
kind: Integration
metadata:
  name: gitlark
spec:
  forProvider:
    apiIdRef:
      name: gitlark
    integrationType: AWS_PROXY
    integrationURI: arn:aws-cn:lambda:cn-northwest-1:035532479701:function:gitlark
    payloadFormatVersion: "2.0"
    region: cn-northwest-1
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: apigatewayv2.aws.crossplane.io/v1alpha1
kind: Route
metadata:
  name: gitlark
spec:
  forProvider:
    apiIdRef:
      name: gitlark
    region: cn-northwest-1
    routeKey: ANY /gitlark
    targetRef:
      name: gitlark
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: apigatewayv2.aws.crossplane.io/v1alpha1
kind: Stage
metadata:
  name: gitlark-prod
spec:
  forProvider:
    apiIdRef:
      name: gitlark
    region: cn-northwest-1
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: apigatewayv2.aws.crossplane.io/v1alpha1
kind: Deployment
metadata:
  name: gitlark
spec:
  forProvider:
    apiIdRef:
      name: gitlark
    region: cn-northwest-1
    stageNameRef:
      name: gitlark-prod
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
