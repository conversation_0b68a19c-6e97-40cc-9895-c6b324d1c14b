---
apiVersion: apigatewayv2.aws.crossplane.io/v1alpha1
kind: API
metadata:
  name: vendorsync
spec:
  forProvider:
    description: sync vendor info cross bitables
    name: vendorsync
    protocolType: HTTP
    region: cn-northwest-1
    routeSelectionExpression: ${request.method} ${request.path}
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: apigatewayv2.aws.crossplane.io/v1alpha1
kind: Integration
metadata:
  name: vendorsync
spec:
  forProvider:
    apiIdRef:
      name: vendorsync
    integrationType: AWS_PROXY
    integrationURI: arn:aws-cn:lambda:cn-northwest-1:035532479701:function:vendorsync
    payloadFormatVersion: "2.0"
    region: cn-northwest-1
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: apigatewayv2.aws.crossplane.io/v1alpha1
kind: Route
metadata:
  name: vendorsync
spec:
  forProvider:
    apiIdRef:
      name: vendorsync
    region: cn-northwest-1
    routeKey: ANY /vendorsync
    targetRef:
      name: vendorsync
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: apigatewayv2.aws.crossplane.io/v1alpha1
kind: Stage
metadata:
  name: vendorsync-prod
spec:
  forProvider:
    apiIdRef:
      name: vendorsync
    region: cn-northwest-1
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: apigatewayv2.aws.crossplane.io/v1alpha1
kind: Deployment
metadata:
  name: vendorsync
spec:
  forProvider:
    apiIdRef:
      name: vendorsync
    region: cn-northwest-1
    stageNameRef:
      name: vendorsync-prod
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
