apiVersion: iam.aws.crossplane.io/v1beta1
kind: Policy
metadata:
  name: prod-lambda-lark
spec:
  deletionPolicy: Delete
  forProvider:
    document: |
      {
        "Statement": [
          {
            "Action": "logs:CreateLogGroup",
            "Effect": "Allow",
            "Resource": "arn:aws-cn:logs:cn-northwest-1:035532479701:*"
          },
          {
            "Action": [
              "logs:CreateLogStream",
              "logs:PutLogEvents"
            ],
            "Effect": "Allow",
            "Resource": [
              "arn:aws-cn:logs:cn-northwest-1:035532479701:log-group:/aws/lambda/*:*"
            ]
          },
          {
            "Action": [
              "ec2:AttachVolume",
              "ec2:CreateTags",
              "ec2:CreateVolume",
              "ec2:DeleteTags",
              "ec2:DeleteVolume",
              "ec2:DescribeInstanceAttribute",
              "ec2:DescribeInstanceStatus",
              "ec2:DescribeInstanceTypes",
              "ec2:DescribeInstances",
              "ec2:DescribeVolumes",
              "ec2:DetachVolume",
              "ec2:RunInstances",
              "ec2:StopInstances",
              "ec2:TerminateInstances"
            ],
            "Effect": "Allow",
            "Resource": "*"
          },
          {
            "Action": [
                "s3:DeleteObject",
                "s3:DeleteObjectVersion",
                "s3:GetObject",
                "s3:PutObject"
            ],
            "Effect": "Allow",
            "Resource": "arn:aws-cn:s3:::prod-lambda-zip/*",
            "Sid": "objectPermission"
          },
          {
              "Action": [
                  "s3:ListAllMyBuckets",
                  "s3:ListJobs",
                  "s3:ListStorageLensConfigurations"
              ],
              "Effect": "Allow",
              "Resource": "*",
              "Sid": "allPermission"
          },
          {
              "Action": [
                  "s3:ListBucket",
                  "s3:ListBucketVersions"
              ],
              "Effect": "Allow",
              "Resource": "arn:aws-cn:s3:::prod-lambda-zip",
              "Sid": "bucketPermission"
          },
          {
            "Sid": "decodepolicy",
            "Effect": "Allow",
            "Action": "sts:DecodeAuthorizationMessage",
            "Resource": "*"
          }
        ],
        "Version": "2012-10-17"
      }
    name: prod-lambda-lark
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
