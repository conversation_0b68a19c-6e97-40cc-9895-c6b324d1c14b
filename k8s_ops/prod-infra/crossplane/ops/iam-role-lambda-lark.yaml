---
apiVersion: iam.aws.crossplane.io/v1beta1
kind: Role
metadata:
  name: prod-lambda-lark
spec:
  deletionPolicy: Delete
  forProvider:
    assumeRolePolicyDocument: |
      {
        "Statement": [
          {
            "Action": "sts:AssumeRole",
            "Effect": "Allow",
            "Principal": {
              "Service": "lambda.amazonaws.com"
            }
          }
        ],
        "Version": "2012-10-17"
      }
    description: lambda lark role to be used
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: iam.aws.crossplane.io/v1beta1
kind: RolePolicyAttachment
metadata:
  name: prod-lambda-lark
spec:
  deletionPolicy: Delete
  forProvider:
    policyArn: arn:aws-cn:iam::035532479701:policy/prod-lambda-lark
    roleNameRef:
      name: prod-lambda-lark
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
