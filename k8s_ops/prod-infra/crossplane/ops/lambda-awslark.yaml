---
apiVersion: lambda.aws.crossplane.io/v1beta1
kind: Function
metadata:
  name: awslark
spec:
  deletionPolicy: Delete
  forProvider:
    code:
      s3BucketRef:
        name: prod-lambda-zip
      s3Key: awslark.zip
    description: lark audit to aws ec2 launch
    environment:
      variables:
        LARKAPPID: ********************
        LARKAPPSEC: pBYpwsQCfQzdUr2Kb44iVfjZkKv4luqR
    # ephemeralStorage:
    #   size: 512
    handler: awslark
    memorySize: 128
    packageType: Zip
    publish: true
    region: cn-northwest-1
    roleRef:
      name: prod-lambda-lark
    runtime: go1.x
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: lambda.aws.crossplane.io/v1alpha1
kind: Permission
metadata:
  name: awslark
spec:
  forProvider:
    action: lambda:InvokeFunction
    functionNameRef:
      name: awslark
    principal: apigateway.amazonaws.com
    region: cn-northwest-1
    sourceARN: arn:aws-cn:execute-api:cn-northwest-1:035532479701:ix5px55uwf/*/*
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
