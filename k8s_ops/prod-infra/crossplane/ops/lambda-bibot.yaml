---
apiVersion: lambda.aws.crossplane.io/v1beta1
kind: Function
metadata:
  name: bitbot
spec:
  deletionPolicy: Delete
  forProvider:
    code:
      s3BucketRef:
        name: prod-lambda-zip
      s3Key: bitbot.zip
    description: Automatically transit status for bitable
    environment:
      variables:
        LARKAPPID: ********************
        LARKAPPSEC: JozlOgO9bRd76ynmiRdRBeOTeTEuKUbF
    # ephemeralStorage:
    #   size: 512
    handler: bitbot
    memorySize: 128
    packageType: Zip
    publish: true
    region: cn-northwest-1
    roleRef:
      name: prod-lambda-lark
    runtime: go1.x
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: lambda.aws.crossplane.io/v1alpha1
kind: Permission
metadata:
  name: bitbot
spec:
  forProvider:
    action: lambda:InvokeFunction
    functionNameRef:
      name: bitbot
    principal: apigateway.amazonaws.com
    region: cn-northwest-1
    sourceARN: arn:aws-cn:execute-api:cn-northwest-1:035532479701:ext8plwcu8/*/*
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
