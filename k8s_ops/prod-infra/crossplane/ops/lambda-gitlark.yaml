---
apiVersion: lambda.aws.crossplane.io/v1beta1
kind: Function
metadata:
  name: gitlark
spec:
  deletionPolicy: Delete
  forProvider:
    code:
      s3BucketRef:
        name: prod-lambda-zip
      s3Key: gitlark.zip
    description: git merge request to lark message automation
    environment:
      variables:
        LARKAPPID: ********************
        LARKAPPSEC: L3ZS6LGZwhYmShHylbB7kd1z3AoiiuKg
    # ephemeralStorage:
    #   size: 512
    handler: gitlark
    memorySize: 128
    packageType: Zip
    publish: true
    region: cn-northwest-1
    roleRef:
      name: prod-lambda-lark
    runtime: go1.x
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: lambda.aws.crossplane.io/v1alpha1
kind: Permission
metadata:
  name: gitlark
spec:
  forProvider:
    action: lambda:InvokeFunction
    functionNameRef:
      name: gitlark
    principal: apigateway.amazonaws.com
    region: cn-northwest-1
    sourceARN: arn:aws-cn:execute-api:cn-northwest-1:035532479701:hd4pw1vfd2/*/*
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
