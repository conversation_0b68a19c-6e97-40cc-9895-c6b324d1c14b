---
apiVersion: lambda.aws.crossplane.io/v1beta1
kind: Function
metadata:
  name: vendorsync
spec:
  deletionPolicy: Delete
  forProvider:
    code:
      s3BucketRef:
        name: prod-lambda-zip
      s3Key: vendorsync.zip
    description: sync vendor info cross bitables
    environment:
      variables:
        LARKAPPID: ********************
        LARKAPPSEC: XdWFL4arQr4QKl67YURkCeJMQb1NvSlo
    # ephemeralStorage:
    #   size: 512
    handler: vendorsync
    memorySize: 128
    packageType: Zip
    publish: true
    region: cn-northwest-1
    roleRef:
      name: prod-lambda-lark
    runtime: go1.x
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: lambda.aws.crossplane.io/v1alpha1
kind: Permission
metadata:
  name: vendorsync
spec:
  forProvider:
    action: lambda:InvokeFunction
    functionNameRef:
      name: vendorsync
    principal: apigateway.amazonaws.com
    region: cn-northwest-1
    sourceARN: arn:aws-cn:execute-api:cn-northwest-1:035532479701:opu248ur67/*/*
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
