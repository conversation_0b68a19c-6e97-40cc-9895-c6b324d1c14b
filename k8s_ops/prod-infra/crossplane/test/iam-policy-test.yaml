apiVersion: iam.aws.crossplane.io/v1beta1
kind: Policy
metadata:
  name: prod-policy-test
spec:
  deletionPolicy: Delete
  forProvider:
    document: |
      {
        "Version": "2012-10-17",
        "Statement": [
          {
            "Effect": "Allow",
            "Action": "route53:GetChange",
            "Resource": "arn:aws-cn:route53:::change/*"
          },
          {
            "Effect": "Allow",
            "Action": [
              "route53:ChangeResourceRecordSets",
              "route53:ListResourceRecordSets"
            ],
            "Resource": "arn:aws-cn:route53:::hostedzone/*"
          },
          {
            "Effect": "Allow",
            "Action": [
              "route53:GetHostedZone",
              "route53:GetHostedZoneCount",
              "route53:ListHostedZonesByName",
              "route53:ListHostedZonesByVPC",
              "route53:ListHostedZones", 
              "route53:ListResourceRecordSets"
            ],
            "Resource": "*"
          }
        ]
      }
    name: prod-policy-test
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config