---
apiVersion: cloudfront.aws.crossplane.io/v1alpha1
kind: Distribution
metadata:
  name: nonprod-dist-images-public
spec:
  forProvider:
    distributionConfig:
      aliases:
        items:
          - s3npip.d.konvery.com
      comment: nonprod-dist-images-public
      # defaultRootObject: index.html
      defaultCacheBehavior:
        allowedMethods:
          cachedMethods:
            items:
              - GET
              - HEAD
              - OPTIONS
          items:
            - GET
            - HEAD
            - OPTIONS
        forwardedValues:
          cookies:
            forward: none
          queryString: false
        minTTL: 0
        responseHeadersPolicyID: "60669652-455b-4ae9-85a4-c4c02393f86c"
        targetOriginID: s3Origin
        trustedKeyGroups:
          enabled: false
        viewerProtocolPolicy: redirect-to-https
      enabled: true
      origins:
        items:
          - domainName: konvery-images-public-nonprod.s3.cn-northwest-1.amazonaws.com.cn
            id: s3Origin
            s3OriginConfig:
              originAccessIdentity: ""
      viewerCertificate:
        iamCertificateID: ASCAQQRPGTTKRUZ2OFMLB
        minimumProtocolVersion: TLSv1.2_2018
        sslSupportMethod: sni-only
    region: cn-northwest-1
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config