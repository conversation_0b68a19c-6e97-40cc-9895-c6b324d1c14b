---
apiVersion: cloudfront.aws.crossplane.io/v1alpha1
kind: Distribution
metadata:
  name: prod-dist-sansheng
spec:
  forProvider:
    distributionConfig:
      aliases:
        items:
          - s3pdss.d.konvery.com
      comment: prod-dist-sansheng
      # defaultRootObject: index.html
      defaultCacheBehavior:
        allowedMethods:
          cachedMethods:
            items:
              - GET
              - HEAD
              - OPTIONS
          items:
            - GET
            - HEAD
            - OPTIONS
        forwardedValues:
          cookies:
            forward: none
          queryString: false
        minTTL: 0
        responseHeadersPolicyID: "60669652-455b-4ae9-85a4-c4c02393f86c"
        targetOriginID: s3Origin
        trustedKeyGroups:
          enabled: true
          items:
            - 231992df-4e13-46f6-b08e-750e41e71361
        viewerProtocolPolicy: redirect-to-https
      enabled: true
      origins:
        items:
          - domainName: prod-workload-sansheng.s3.cn-northwest-1.amazonaws.com.cn
            id: s3Origin
            s3OriginConfig:
              originAccessIdentity: origin-access-identity/cloudfront/E1BUFGQM1U5PX1
      viewerCertificate:
        iamCertificateID: ASCAQQRPGTTKRUZ2OFMLB
        minimumProtocolVersion: TLSv1.2_2018
        sslSupportMethod: sni-only
    region: cn-northwest-1
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config