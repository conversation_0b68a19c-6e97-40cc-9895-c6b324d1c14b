apiVersion: ec2.aws.crossplane.io/v1beta1
kind: SecurityGroup
metadata: 
  name: prod-workload-postgresql-sg
spec:
  deletionPolicy: Delete
  forProvider:
    description: Security group for database in prod workload vpc
    groupName: prod-workload-postgresql-sg
    region: cn-northwest-1
    ingress: 
      - fromPort: 5432
        toPort: 5432
        ipProtocol: TCP
        userIdGroupPairs:
          - groupId: "sg-0c4417d10ec7a10df"
            description: "Allow all workload eks workers to access database"
    vpcId: vpc-0c33db09634821b16 # prod-workload vpc id
    tags:
      - key: Name
        value: prod-workload-postgresql-sg
      - key: owner
        value: sre
      - key: profile
        value: prod
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
