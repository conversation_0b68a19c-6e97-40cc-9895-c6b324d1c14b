apiVersion: iam.aws.crossplane.io/v1beta1
kind: Role
metadata:
  name: prod-workload-ebs-csi-driver
spec:
  deletionPolicy: Delete
  forProvider:
    assumeRolePolicyDocument: |
      {
        "Version": "2012-10-17",
        "Statement": [
          {
            "Effect": "Allow",
            "Action": "sts:AssumeRoleWithWebIdentity",
            "Principal": {
              "Federated": "arn:aws-cn:iam::************:oidc-provider/oidc.eks.cn-northwest-1.amazonaws.com.cn/id/F660A3D0B6AA72092D1987CCC42C383B"
            },
            "Condition": {
              "StringEquals": {
                "oidc.eks.cn-northwest-1.amazonaws.com.cn/id/F660A3D0B6AA72092D1987CCC42C383B:sub": "system:serviceaccount:kube-system:ebs-csi-controller-sa"
              }
            }
          }
        ]
      }
    description: aws-ebs-csi-driver to be used in prod-workload eks cluster
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: iam.aws.crossplane.io/v1beta1
kind: RolePolicyAttachment
metadata: 
  name: prod-workload-ebs-csi-driver
spec: 
  deletionPolicy: Delete
  forProvider:
    roleNameRef:
      name: prod-workload-ebs-csi-driver
    policyArn: arn:aws-cn:iam::aws:policy/service-role/AmazonEBSCSIDriverPolicy
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
