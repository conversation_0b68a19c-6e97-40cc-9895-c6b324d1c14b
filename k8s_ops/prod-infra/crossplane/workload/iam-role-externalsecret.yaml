apiVersion: iam.aws.crossplane.io/v1beta1
kind: Role
metadata:
  name: prod-workload-externalsecret
spec:
  deletionPolicy: Delete
  forProvider:
    assumeRolePolicyDocument: |
      {
        "Version": "2012-10-17",
        "Statement": [
          {
            "Effect": "Allow",
            "Action": "sts:AssumeRoleWithWebIdentity",
            "Principal": {
              "Federated": "arn:aws-cn:iam::************:oidc-provider/oidc.eks.cn-northwest-1.amazonaws.com.cn/id/F660A3D0B6AA72092D1987CCC42C383B"
            },
            "Condition": {
              "StringEquals": {
                "oidc.eks.cn-northwest-1.amazonaws.com.cn/id/F660A3D0B6AA72092D1987CCC42C383B:sub": "system:serviceaccount:infra:external-secrets"
              }
            }
          }
        ]
      }
    description: external-secrets to be used in prod-workload eks cluster
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: iam.aws.crossplane.io/v1beta1
kind: RolePolicyAttachment
metadata: 
  name: prod-workload-externalsecret
spec: 
  deletionPolicy: Delete
  forProvider:
    roleNameRef:
      name: prod-workload-externalsecret
    policyArn: arn:aws-cn:iam::************:policy/prod-secret-manager
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config