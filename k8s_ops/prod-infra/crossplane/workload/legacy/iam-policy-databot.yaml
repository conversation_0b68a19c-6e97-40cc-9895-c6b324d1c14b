apiVersion: iam.aws.crossplane.io/v1beta1
kind: Policy
metadata:
  name: temp-annotation-ecarx
spec:
  deletionPolicy: Delete
  forProvider:
    document: |
      {
          "Statement": [
              {
                "Action": [
                    "s3:*",
                    "s3-object-lambda:*"
                ],
                "Effect": "Allow",
                "Resource": "arn:aws-cn:s3:::temp-annotation-ecarx/*",
                "Sid": "objectPermission"
              },
              {
                "Action": [
                    "s3:ListBucket",
                    "s3:GetBucketLocation",
                    "s3:ListBucketVersions",
                    "s3:AbortMultipartUpload",
                    "s3:ListMultipartUploadParts",
                    "s3:ListBucketMultipartUploads"
                ],
                "Effect": "Allow",
                "Resource": "arn:aws-cn:s3:::temp-annotation-ecarx",
                "Sid": "bucketPermission"
              }
          ],
          "Version": "2012-10-17"
      }
    name: temp-annotation-ecarx
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: iam.aws.crossplane.io/v1beta1
kind: Policy
metadata:
  name: temp-annotation-deeproute
spec:
  deletionPolicy: Delete
  forProvider:
    document: |
      {
          "Statement": [
              {
                "Action": [
                    "s3:*",
                    "s3-object-lambda:*"
                ],
                "Effect": "Allow",
                "Resource": "arn:aws-cn:s3:::temp-annotation-deeproute/*",
                "Sid": "objectPermission"
              },
              {
                "Action": [
                    "s3:ListBucket",
                    "s3:GetBucketLocation",
                    "s3:ListBucketVersions",
                    "s3:AbortMultipartUpload",
                    "s3:ListMultipartUploadParts",
                    "s3:ListBucketMultipartUploads"
                ],
                "Effect": "Allow",
                "Resource": "arn:aws-cn:s3:::temp-annotation-deeproute",
                "Sid": "bucketPermission"
              }
          ],
          "Version": "2012-10-17"
      }
    name: temp-annotation-deeproute
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: iam.aws.crossplane.io/v1beta1
kind: Policy
metadata:
  name: temp-annotation-konvery
spec:
  deletionPolicy: Delete
  forProvider:
    document: |
      {
          "Statement": [
              {
                "Action": [
                    "s3:*",
                    "s3-object-lambda:*"
                ],
                "Effect": "Allow",
                "Resource": "arn:aws-cn:s3:::temp-annotation-konvery/*",
                "Sid": "objectPermission"
              },
              {
                "Action": [
                    "s3:ListBucket",
                    "s3:GetBucketLocation",
                    "s3:ListBucketVersions",
                    "s3:AbortMultipartUpload",
                    "s3:ListMultipartUploadParts",
                    "s3:ListBucketMultipartUploads"
                ],
                "Effect": "Allow",
                "Resource": "arn:aws-cn:s3:::temp-annotation-konvery",
                "Sid": "bucketPermission"
              }
          ],
          "Version": "2012-10-17"
      }
    name: temp-annotation-konvery
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: iam.aws.crossplane.io/v1beta1
kind: Policy
metadata:
  name: temp-annotation-huixi
spec:
  deletionPolicy: Delete
  forProvider:
    document: |
      {
          "Statement": [
              {
                "Action": [
                    "s3:*",
                    "s3-object-lambda:*"
                ],
                "Effect": "Allow",
                "Resource": "arn:aws-cn:s3:::temp-annotation-huixi/*",
                "Sid": "objectPermission"
              },
              {
                "Action": [
                    "s3:ListBucket",
                    "s3:GetBucketLocation",
                    "s3:ListBucketVersions",
                    "s3:AbortMultipartUpload",
                    "s3:ListMultipartUploadParts",
                    "s3:ListBucketMultipartUploads"
                ],
                "Effect": "Allow",
                "Resource": "arn:aws-cn:s3:::temp-annotation-huixi",
                "Sid": "bucketPermission"
              }
          ],
          "Version": "2012-10-17"
      }
    name: temp-annotation-huixi
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
