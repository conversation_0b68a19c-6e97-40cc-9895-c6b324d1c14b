apiVersion: iam.aws.crossplane.io/v1beta1
kind: User
metadata:
  name: prod-databot
spec:
  deletionPolicy: Delete
  forProvider:
    tags:
      - key: usage
        value: robot to generate presigned S3 URL
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: iam.aws.crossplane.io/v1beta1
kind: AccessKey
metadata:
  name: prod-databot
spec:
  forProvider:
    userNameRef:
      name: prod-databot
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
  writeConnectionSecretToRef:
    name: prod-databot-auth
    namespace: crossplane-system
---
apiVersion: iam.aws.crossplane.io/v1beta1
kind: UserPolicyAttachment
metadata: 
  name: prod-databot-ecarx
spec: 
  deletionPolicy: Delete
  forProvider:
    userNameRef:
      name: prod-databot
    policyArn: arn:aws-cn:iam::035532479701:policy/temp-annotation-ecarx
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: iam.aws.crossplane.io/v1beta1
kind: UserPolicyAttachment
metadata: 
  name: prod-databot-deeproute
spec: 
  deletionPolicy: Delete
  forProvider:
    userNameRef:
      name: prod-databot
    policyArn: arn:aws-cn:iam::035532479701:policy/temp-annotation-deeproute
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: iam.aws.crossplane.io/v1beta1
kind: UserPolicyAttachment
metadata: 
  name: prod-databot-konvery
spec: 
  deletionPolicy: Delete
  forProvider:
    userNameRef:
      name: prod-databot
    policyArn: arn:aws-cn:iam::035532479701:policy/temp-annotation-konvery
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: iam.aws.crossplane.io/v1beta1
kind: UserPolicyAttachment
metadata: 
  name: prod-databot-konvery
spec: 
  deletionPolicy: Delete
  forProvider:
    userNameRef:
      name: prod-databot
    policyArn: arn:aws-cn:iam::035532479701:policy/temp-annotation-huixi
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
