apiVersion: route53.aws.crossplane.io/v1alpha1
kind: HostedZone
metadata: 
  name: d-konvery-com
spec: 
  deletionPolicy: Delete
  forProvider:
    name: d.konvery.com
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
# ---
# apiVersion: route53.aws.crossplane.io/v1alpha1
# kind: ResourceRecordSet
# metadata: 
#   name: np.d.konvery.com
# spec:
#   deletionPolicy: Delete
#   forProvider:
#     ttl: 600
#     type: NS
#     resourceRecords:
#       - value: ns-2063.awsdns-cn-00.biz.
#       - value: ns-842.awsdns-cn-52.com.
#       - value: ns-1383.awsdns-cn-22.net.
#       - value: ns-3813.awsdns-cn-46.cn.
#       - value: ns-intl-842.awsdns-cn-52.com.
#       - value: ns-intl-3813.awsdns-cn-46.cn.
#     zoneId: Z0186407BXHVC4TGXLKQ
#   providerConfigRef:
#     name: prod-infra-crossplane-provider-aws-config

