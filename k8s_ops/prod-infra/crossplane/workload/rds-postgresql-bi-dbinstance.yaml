---
apiVersion: rds.aws.crossplane.io/v1alpha1
kind: DBInstance
metadata:
  name: prod-workload-postgresql-bi-instance
spec:
  forProvider:
    autoMinorVersionUpgrade: true
    dbClusterIdentifier: prod-workload-postgresql-bi
    dbInstanceClass: db.t4g.medium
    dbParameterGroupName: default.aurora-postgresql15
    dbSubnetGroupName: prod-workload
    enablePerformanceInsights: true
    engine: aurora-postgresql
    engineVersion: "15.3"
    licenseModel: postgresql-license
    promotionTier: 1
    publiclyAccessible: false
    region: cn-northwest-1
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: rds.aws.crossplane.io/v1alpha1
kind: DBInstance
metadata:
  name: prod-workload-postgresql-bi-instance-replica
spec:
  forProvider:
    autoMinorVersionUpgrade: true
    dbClusterIdentifier: prod-workload-postgresql-bi
    dbInstanceClass: db.t4g.medium
    dbParameterGroupName: default.aurora-postgresql15
    dbSubnetGroupName: prod-workload
    enablePerformanceInsights: true
    engine: aurora-postgresql
    engineVersion: "15.3"
    licenseModel: postgresql-license
    promotionTier: 2
    publiclyAccessible: false
    region: cn-northwest-1
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
