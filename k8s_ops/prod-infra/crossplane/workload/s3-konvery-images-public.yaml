---
apiVersion: s3.aws.crossplane.io/v1beta1
kind: Bucket
metadata:
  name: konvery-images-public
spec:
  deletionPolicy: Orphan
  forProvider:
    corsConfiguration:
      corsRules:
        - allowedMethods:
            - GET
            - HEAD
            # - OPTIONS
          allowedOrigins:
            - "*"
    # acl: public-read
    locationConstraint: cn-northwest-1
    objectOwnership: BucketOwnerEnforced
    publicAccessBlockConfiguration:
      blockPublicAcls: false
      blockPublicPolicy: false
      ignorePublicAcls: false
      restrictPublicBuckets: false
    serverSideEncryptionConfiguration:
      rules:
        - applyServerSideEncryptionByDefault:
            sseAlgorithm: AES256
    tagging:
      tagSet:
        - key: owner
          value: konvery-sre
    policy:
      version: '2012-10-17'
      statements:
        - action:
            - s3:GetObject
            - s3:GetObjectVersion
          effect: Allow
          principal:
            allowAnon: true
          resource:
            - arn:aws-cn:s3:::konvery-images-public/*
          sid: PublicRead
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
