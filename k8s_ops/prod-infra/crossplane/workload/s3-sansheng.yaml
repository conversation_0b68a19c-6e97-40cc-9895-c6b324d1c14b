---
apiVersion: s3.aws.crossplane.io/v1beta1
kind: Bucket
metadata:
  name: prod-workload-sansheng
spec:
  deletionPolicy: Orphan
  forProvider:
    acl: private
    locationConstraint: cn-northwest-1
    publicAccessBlockConfiguration:
      blockPublicAcls: true
      blockPublicPolicy: true
      ignorePublicAcls: true
      restrictPublicBuckets: true
    serverSideEncryptionConfiguration:
      rules:
        - applyServerSideEncryptionByDefault:
            sseAlgorithm: AES256
    corsConfiguration:
      corsRules:
        - allowedHeaders:
            - Content-Type
          allowedMethods:
            - GET
            - HEAD
            - POST
            - PUT
          allowedOrigins:
            - 'https://admin.d.konvery.com'
            - 'https://label.d.konvery.com'
            - 'https://tars.d.konvery.com'
            - 'null'
          exposeHeaders:
            - 'ETag'
    lifecycleConfiguration:
      rules:
        - status: Enabled
          abortIncompleteMultipartUpload:
            daysAfterInitiation: 7
    tagging:
      tagSet:
        - key: owner
          value: konvery-sre
    policy:
      version: '2012-10-17'
      statements:
        - action:
            - s3:GetObject
            - s3:GetObjectVersion
          effect: Allow
          principal:
            awsPrincipals: 
              - iamUserArn: "arn:aws-cn:iam::cloudfront:user/CloudFront Origin Access Identity E1BUFGQM1U5PX1"
          resource:
            - arn:aws-cn:s3:::prod-workload-sansheng/*
          sid: CloudfrontRead      
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
