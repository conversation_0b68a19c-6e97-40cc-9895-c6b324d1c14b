---
apiVersion: s3.aws.crossplane.io/v1beta1
kind: Bucket
metadata:
  name: prod-workload-sentry
spec:
  deletionPolicy: Orphan
  forProvider:
    acl: private
    locationConstraint: cn-northwest-1
    publicAccessBlockConfiguration:
      blockPublicAcls: true
      blockPublicPolicy: true
      ignorePublicAcls: true
      restrictPublicBuckets: true
    serverSideEncryptionConfiguration:
      rules:
        - applyServerSideEncryptionByDefault:
            sseAlgorithm: AES256
    tagging:
      tagSet:
        - key: owner
          value: konvery-sre
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
