apiVersion: secretsmanager.aws.crossplane.io/v1beta1
kind: Secret
metadata:
  name: wildcard-d-konvery-com
spec:
  forProvider:
    region: cn-northwest-1
    description: "production konvery com certificate"
    forceDeleteWithoutRecovery: true
    #recoveryWindowInDays: 7
    stringSecretRef:
      name: wildcard-d-konvery-com
      namespace: infra
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
