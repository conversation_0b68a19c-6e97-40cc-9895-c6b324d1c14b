apiVersion: secretsmanager.aws.crossplane.io/v1beta1
kind: Secret
metadata:
  name: wildcard-rp-konvery-work
spec:
  forProvider:
    region: cn-northwest-1
    description: "production konvery work certificate"
    forceDeleteWithoutRecovery: true
    #recoveryWindowInDays: 7
    stringSecretRef:
      name: wildcard-rp-konvery-work
      namespace: infra
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
