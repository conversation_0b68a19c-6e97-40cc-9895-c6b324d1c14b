---
apiVersion: sns.aws.crossplane.io/v1beta1
kind: Topic
metadata:
  name: prod-workload-sns-sansheng
spec:
  forProvider:
    name: prod-workload-sns-sansheng
    region: cn-northwest-1
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
  writeConnectionSecretToRef:
    name: prod-workload-sns-sansheng
    namespace: infra
# ---
# apiVersion: sns.aws.crossplane.io/v1beta1
# kind: Subscription
# metadata:
#   name: prod-workload-sns-sansheng
# spec:
#   forProvider:
#     endpoint: arn:aws-cn:sqs:cn-northwest-1:035532479701:prod-workload-sqs-sansheng
#     protocol: sqs
#     rawMessageDelivery: "true"
#     region: cn-northwest-1
#     topicArnRef:
#       name: prod-workload-sns-sansheng
#   providerConfigRef:
#     name: prod-infra-crossplane-provider-aws-config
---
apiVersion: sns.aws.crossplane.io/v1beta1
kind: Topic
metadata:
  name: prod-workload-sns-sansheng-anno
spec:
  forProvider:
    name: prod-workload-sns-sansheng-anno
    region: cn-northwest-1
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
  writeConnectionSecretToRef:
    name: prod-workload-sns-sansheng-anno
    namespace: infra
# ---
# apiVersion: sns.aws.crossplane.io/v1beta1
# kind: Subscription
# metadata:
#   name: prod-workload-sns-sansheng-anno
# spec:
#   forProvider:
#     endpoint: arn:aws-cn:sqs:cn-northwest-1:035532479701:prod-workload-sqs-sansheng-anno
#     protocol: sqs
#     rawMessageDelivery: "true"
#     region: cn-northwest-1
#     topicArnRef:
#       name: prod-workload-sns-sansheng-anno
#   providerConfigRef:
#     name: prod-infra-crossplane-provider-aws-config
---
apiVersion: sns.aws.crossplane.io/v1beta1
kind: Subscription
metadata:
  name: prod-workload-sns-sansheng-annostat
spec:
  forProvider:
    endpoint: arn:aws-cn:sqs:cn-northwest-1:035532479701:prod-workload-sqs-sansheng-annostat
    protocol: sqs
    rawMessageDelivery: "true"
    region: cn-northwest-1
    topicArnRef:
      name: prod-workload-sns-sansheng-anno
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
---
apiVersion: sns.aws.crossplane.io/v1beta1
kind: Subscription
metadata:
  name: prod-workload-sns-sansheng-annofeed
spec:
  forProvider:
    endpoint: arn:aws-cn:sqs:cn-northwest-1:035532479701:prod-workload-sqs-sansheng-annofeed
    protocol: sqs
    rawMessageDelivery: "true"
    region: cn-northwest-1
    topicArnRef:
      name: prod-workload-sns-sansheng-anno
  providerConfigRef:
    name: prod-infra-crossplane-provider-aws-config
