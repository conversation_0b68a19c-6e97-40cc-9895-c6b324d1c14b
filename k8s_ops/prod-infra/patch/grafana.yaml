stringData:
  grafana.ini: |
    [server]
    root_url = https://grafana.rp.konvery.work
    [auth.generic_oauth]
    name = Keycloak
    enabled = true
    allow_sign_up = true
    client_id = grafana-rp
    client_secret = WZHwYLtGpwPf7BB2GRhkRePigLlgvNdo
    scopes = openid roles profile email web-origins
    allow_assign_grafana_admin = true
    auth_url = https://keycloak.rp.konvery.work/realms/konvery/protocol/openid-connect/auth
    token_url = https://keycloak.rp.konvery.work/realms/konvery/protocol/openid-connect/token
    api_url = https://keycloak.rp.konvery.work/realms/konvery/protocol/openid-connect/userinfo
    role_attribute_path = "contains(realm_access.roles[*], 'kvy-admin') && 'Admin' || contains(realm_access.roles[*], 'kvy-editor') && 'Editor' || 'Viewer'"
    [date_formats]
    default_timezone = Asia/Shanghai
    [security]
    admin_password = xfBHvv9OzF4Zr04b
    [users]
    auto_assign_org = true
