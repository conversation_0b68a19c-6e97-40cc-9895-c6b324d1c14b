# Non production workload cluster setup

## Preparing Environment

```shell
aws eks describe-cluster --name prod-workload-cluster --query "cluster.identity.oidc.issuer" --output text
https://oidc.eks.cn-northwest-1.amazonaws.com.cn/id/F660A3D0B6AA72092D1987CCC42C383B
```

```shell
aws eks update-kubeconfig --region cn-northwest-1 --name prod-workload-cluster
```

### Update cluster role, user, and user group

```shell
# Update cluster role
kubectl apply -f k8s_ops/misc/cluster-role/kvy-clusterrole.yaml
# Cluster role bindings
kubectl apply -f k8s_ops/misc/cluster-role/cluster-role-binding.yaml

# Patch aws-auth to map users, user groups, and roles
kubectl -n kube-system patch configmap aws-auth --patch-file k8s_ops/prod-workload/patch/aws-auth.yaml
```

## Infra perspective: provisioning domain, database and role for workload

### chang to infra cluster context for crossplane provider-aws ops

```shell
kubectx arn:aws-cn:eks:cn-northwest-1:035532479701:cluster/prod-infra-cluster
```

### associate vpc to rp.konvery.work

vpc-0c33db09634821b16 to Z091788020XZDIQ2LACM1

### r53 for d.konvery.com

```shell
kaf k8s_ops/prod-infra/crossplane/workload/r53-zone-d-konvery-com.yaml
```

### cert for d-konvery-com

```shell
kaf k8s_ops/prod-infra/crds/cert-clusterissuer-d-konvery-com.yaml
kaf k8s_ops/prod-infra/crds/cert-wildcard-d-konvery-com.yaml

kaf k8s_ops/prod-infra/crossplane/workload/sec-wildcard-d-konvery-com.yaml
kaf k8s_ops/prod-infra/crossplane/workload/sec-wildcard-rp-konvery-work.yaml
```

### database

```shell
# database security group
kaf k8s_ops/prod-infra/crossplane/workload/ec2-sg-postgresql.yaml

# manually init db passwd
## To be changed by community, PR raised
kubectl create secret generic manual-initial-db-pw-workload 
--from-literal=password=<passwd> -n crossplane-system

# install rds for all infra shared DB Cluster
kaf k8s_ops/prod-infra/crossplane/workload/rds-postgresql-dbcluster.yaml

# install rds instance as rw node in DB Cluster
kaf k8s_ops/prod-infra/crossplane/workload/rds-postgresql-dbinstance.yaml

# push rds secrets to aws secret manager
kaf k8s_ops/prod-infra/crossplane/workload/sec-postgresql-secret.yaml
```

### role for middleware

Only need to bind corresponding policy to roles created

```shell
# external-dns role
kaf k8s_ops/prod-infra/crossplane/workload/iam-role-externaldns.yaml

# external-secret role
kaf k8s_ops/prod-infra/crossplane/workload/iam-role-externalsecret.yaml
```

## Workload perspective: setup & provisioning middleware

### chang to workload cluster context

```shell
kubectx arn:aws-cn:eks:cn-northwest-1:035532479701:cluster/prod-workload-cluster
```

### priority class

```shell
kaf k8s_ops/prod-workload/crds/priorityclass.yaml
```

### Metrics server

```shell
# https://docs.aws.amazon.com/eks/latest/userguide/metrics-server.html
# kubectl apply -f https://github.com/kubernetes-sigs/metrics-server/releases/latest/download/components.yaml
kaf k8s_ops/misc/metrics-server.yaml

kubectl -n kube-system patch deployment metrics-server --patch-file k8s_ops/prod-workload/patch/metrics-server.yaml
```

### aws-ebs-csi-driver

``` shell
# !!! infra context
kaf k8s_ops/prod-infra/crossplane/workload/iam-role-aws-ebs-csi-driver.yaml

# using aws managed ebs csi driver
kaf k8s_ops/prod-workload/crds/storageclass.yaml

# !NOTE: restart pod ebs-csi-controller-* to make env "AWS_ROLE_ARN" etc injected to pod

# make ebs-sc the only default driver
kubectl patch storageclass gp2 -p '{"metadata": {"annotations":{"storageclass.kubernetes.io/is-default-class":"false"}}}'
```

### Install ArgoCD

```shell
kubectl create namespace argocd
# kubectl apply -n argocd -f https://raw.githubusercontent.com/argoproj/argo-cd/stable/manifests/install.yaml
# kubectl apply -n argocd -f https://raw.githubusercontent.com/argoproj/argo-cd/v2.4.8/manifests/install.yaml
# kubectl apply -n argocd -f https://raw.githubusercontent.com/argoproj/argo-cd/v2.4.4/manifests/install.yaml

# wget -e use_proxy=yes -e https_proxy=127.0.0.1:7890 https://raw.githubusercontent.com/argoproj/argo-cd/v2.4.0/manifests/install.yaml
# kubectl apply -n argocd -f install.yaml

# Patch node to be scheduled on ON_DEMAND node
kubectl -n argocd patch deployment argocd-applicationset-controller --patch-file k8s_ops/prod-workload/patch/argocd.yaml
kubectl -n argocd patch deployment argocd-dex-server --patch-file k8s_ops/prod-workload/patch/argocd-dex.yaml
kubectl -n argocd patch deployment argocd-notifications-controller --patch-file k8s_ops/prod-workload/patch/argocd.yaml
kubectl -n argocd patch deployment argocd-redis --patch-file k8s_ops/prod-workload/patch/argocd.yaml
kubectl -n argocd patch deployment argocd-repo-server --patch-file k8s_ops/prod-workload/patch/argocd.yaml
kubectl -n argocd patch deployment argocd-server --patch-file k8s_ops/prod-workload/patch/argocd.yaml

kaf k8s_ops/prod-workload/crds/argocd-appproj-infra.yaml

# Retrieve passwords
kubectl get secret -n argocd argocd-initial-admin-secret -o json | jq ".data.password | @base64d"
```

### ArgoCD upgrade

```shell
# upgradeing argocd
kubectl apply -n argocd -f https://raw.githubusercontent.com/argoproj/argo-cd/stable/manifests/install.yaml
# updating dex file and patch
kubectl -n argocd patch deployment argocd-dex-server --patch-file k8s_ops/prod-workload/patch/argocd-dex.yaml
# Configure argocd oidc
kaf k8s_ops/prod-workload/crds/configmap-argocd.yaml
```

### Install crossplane

Note: we only enable db operations for workload cluster

```shell
kaf k8s_ops/prod-workload/argocd/crossplane.yaml
kaf k8s_ops/prod-workload/crossplane/provider/provider-postgresql.yaml
```

<!-- ### Install cert-manager

```shell
kaf k8s_ops/prod-workload/argocd/cert-manager.yaml

kaf k8s_ops/prod-workload/crds/cert-clusterissuer-np-api-konvery-com.yaml
kaf k8s_ops/prod-workload/crds/cert-wildcard-np-api-konvery-com.yaml
``` -->
### Install external-secret

```shell
kaf k8s_ops/prod-workload/argocd/external-secret.yaml

# retrieve rds role secret
kaf k8s_ops/prod-workload/crds/extsec-postgresql-role.yaml
```

### Install external-dns

```shell
kaf k8s_ops/prod-workload/argocd/external-dns-internal.yaml
kaf k8s_ops/prod-workload/argocd/external-dns-external.yaml
```

### Install ingress-nginx

```shell
kaf k8s_ops/prod-workload/argocd/ingress-nginx-internal.yaml
kaf k8s_ops/prod-workload/argocd/ingress-nginx-external.yaml
```

### Configure argocd

```shell
# retrieve certificate
kaf k8s_ops/prod-workload/crds/extsec-wildcard-rp-konvery-work.yaml

# export ingress
kaf k8s_ops/prod-workload/crds/ingress-argocd-workload.yaml

# oidc config
## !!!NOTE: infra context
kaf k8s_ops/prod-infra/crds/keycloak-client-argocd-workload-rp.yaml

## !!!NOTE: workload context
kaf k8s_ops/prod-workload/crds/configmap-argocd.yaml
```

## Application deployment

### apisix

```shell
kaf k8s_ops/prod-workload/argocd/etcd.yaml
kaf k8s_ops/prod-workload/argocd/apisix.yaml
kaf k8s_ops/prod-workload/crds/ingress-apisix-dashboard.yaml
```

### temporal

```shell
kaf k8s_ops/prod-workload/crossplane/postgresql-role-temporal.yaml
kaf k8s_ops/prod-workload/crossplane/postgresql-config-temporal.yaml
# Be careful with crossplane annotations
kaf k8s_ops/prod-workload/crossplane/postgresql-db-temporal.yaml

# launch alpine
kaf k8s_ops/misc/alpine.yaml
sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
apk add git go make
git clone https://gitlab.rp.konvery.work/infra/temporal.git
go env -w GOPROXY=https://goproxy.cn
cd temporal
git checkout v1.20.2
make temporal-sql-tool
export SQL_USER=temporal
export SQL_PASSWORD=aAQb2WFo2Kg3xHnS58SGnOiP2xy
export SQL_HOST=prod-workload-postgresql.cluster-ck7i7eqx0frz.rds.cn-northwest-1.amazonaws.com.cn
export TEMPORAL_DB=temporal
export VISIBILITY_DB=temporal_visibility
./temporal-sql-tool -ep ${SQL_HOST} -u ${SQL_USER} -p 5432 --pl postgres --db ${TEMPORAL_DB} setup -v 0.0
./temporal-sql-tool -ep ${SQL_HOST} -u ${SQL_USER} -p 5432 --pl postgres --db ${VISIBILITY_DB} setup -v 0.0
./temporal-sql-tool -u $SQL_USER --pw $SQL_PASSWORD -p 5432 --pl postgres --db $TEMPORAL_DB --ep $SQL_HOST update-schema  -d ./schema/postgresql/v12/temporal/versioned
./temporal-sql-tool -u $SQL_USER --pw $SQL_PASSWORD -p 5432 --pl postgres --db $VISIBILITY_DB --ep $SQL_HOST update-schema update-schema -d ./schema/postgresql/v12/visibility/versioned

kaf k8s_ops/prod-workload/argocd/temporal.yaml
kaf k8s_ops/prod-workload/crds/ingress-temporal.yaml
```

### sentry

```shell
# s3 bucket, iam role and policy
# prod-infra context
kaf k8s_ops/prod-infra/crossplane/workload/s3-sentry.yaml
kaf k8s_ops/prod-infra/crossplane/workload/iam-policy-sentry.yaml
kaf k8s_ops/prod-infra/crossplane/workload/iam-role-sentry.yaml

# db and other peripheral components
# prod-workload context
kaf k8s_ops/prod-workload/crossplane/postgresql-role-sentry.yaml
kaf k8s_ops/prod-workload/crossplane/postgresql-config-sentry.yaml
kaf k8s_ops/prod-workload/crossplane/postgresql-db-sentry.yaml

# sentry deployment
kaf k8s_ops/prod-workload/argocd/sentry.yaml

# Hack topics
# Login to kafka-0
/opt/bitnami/kafka/bin/kafka-topics.sh --create --topic ingest-replay-recordings --bootstrap-server localhost:9092
```

### sansheng

* create app project

```shell
kaf k8s_ops/prod-workload/crds/argocd-appproj-sansheng.yaml
```

* create k8s namespace

```shell
k create namespace sansheng
```

* create service account for s3 access

```shell
# infra context
kaf k8s_ops/prod-infra/crossplane/workload/s3-sansheng.yaml
kaf k8s_ops/prod-infra/crossplane/workload/iam-policy-sansheng.yaml
kaf k8s_ops/prod-infra/crossplane/workload/iam-role-sansheng.yaml
kaf k8s_ops/prod-infra/crossplane/workload/iam-user-sansheng.yaml
kaf k8s_ops/prod-infra/crossplane/workload/sec-user-sansheng.yaml
```

* create postgresql database

```shell
# workload context
kaf k8s_ops/prod-workload/crossplane/postgresql-role-sansheng.yaml
kaf k8s_ops/prod-workload/crossplane/postgresql-config-sansheng.yaml
kaf k8s_ops/prod-workload/crossplane/postgresql-db-sansheng.yaml
```

* create secret for artifactory token & sansheng user

```shell
# workload context
kaf k8s_ops/prod-workload/crds/extsec-user-sansheng.yaml
```

* create ingress for services

```shell
kaf k8s_ops/prod-workload/crds/ingress-apisix-label.yaml
kaf k8s_ops/prod-workload/crds/ingress-apisix-anno.yaml
```

### Install Prometheus

Using deployment of prometheus operator

```shell
# clone repo for k8s v1.26
git clone https://github.com/prometheus-operator/kube-prometheus
git checkout release-0.13

# Create the namespace and CRDs, and then wait for them to be available before creating the remaining resources
# Note that due to some CRD size we are using kubectl server-side apply feature which is generally available since kubernetes 1.22.
# If you are using previous kubernetes versions this feature may not be available and you would need to use kubectl create instead.
k apply --server-side -f manifests/setup
k -n monitoring wait --for condition=Established --all CustomResourceDefinition
kaf manifests/

# affinity context begin
kubectl -n monitoring patch alertmanager.monitoring.coreos.com/main --type merge --patch-file k8s_ops/prod-workload/patch/prometheus-alertmanager.yaml
## + persistentVolume
kubectl -n monitoring patch prometheus.monitoring.coreos.com/k8s --type merge --patch-file k8s_ops/prod-workload/patch/prometheus-k8s.yaml
kubectl -n monitoring patch deployment blackbox-exporter --patch-file k8s_ops/prod-workload/patch/prometheus-blackbox-exporter.yaml
kubectl -n monitoring patch deployment grafana --patch-file k8s_ops/prod-workload/patch/prometheus-grafana.yaml
## + container patch
kubectl -n monitoring patch deployment kube-state-metrics --patch-file k8s_ops/prod-workload/patch/prometheus-kube-state-metrics.yaml
## + container patch
kubectl -n monitoring patch deployment prometheus-adapter --patch-file k8s_ops/prod-workload/patch/prometheus-adapter.yaml
kubectl -n monitoring patch deployment prometheus-operator --patch-file k8s_ops/prod-workload/patch/prometheus-operator.yaml
# affinity context end

# patch cluster role
kubectl patch clusterrole/prometheus-k8s --patch-file k8s_ops/prod-workload/patch/prometheus-k8s-cluster-role.yaml

# delete all network policies
for i in $(kubectl -n monitoring get networkpolicy -o jsonpath='{.items[*].metadata.name}')
do
   kubectl -n monitoring delete networkpolicy $i
done
# upgrade end

# add ingress
kaf k8s_ops/prod-workload/crds/ingress-prometheus-grafana.yaml

# add oidc for keycloak

# prod-infra ctx
kaf k8s_ops/prod-infra/crds/keycloak-client-grafana.yaml
# manually add mapper in the client config (mapper type: User Realm Role, Token Claim Name: realm_access.roles, Claim JSON Type: String)

# prod-workload ctx
kubectl -n monitoring patch secret grafana-config --patch-file k8s_ops/prod-workload/patch/grafana.yaml
```

### Install Loki

```shell
# create pull secret
# refer to k8s_ops/prod-workload/crds/secret-artifactory-token.yaml

# create app
kaf k8s_ops/prod-workload/argocd/loki.yaml
```

### keto

```shell
# postgresql db
kaf k8s_ops/prod-workload/crossplane/postgresql-role-keto.yaml
kaf k8s_ops/prod-workload/crossplane/postgresql-config-keto.yaml
kaf k8s_ops/prod-workload/crossplane/postgresql-db-keto.yaml

# extsec
kaf k8s_ops/prod-workload/argocd/external-secret.yaml
kaf k8s_ops/prod-workload/crds/extsec-keto.yaml

# app
kaf k8s_ops/prod-workload/argocd/keto.yaml
```

## Konvery workload

### Frontend

admin-dashboard
label-tool

### Backend

anno
annofeed
iam

### config

``` shell
# temporal 初始化：
# register the default namespace
tctl namespace register

# keto 初始化：
cd /home/<USER>/
./init-tuples.sh
```
