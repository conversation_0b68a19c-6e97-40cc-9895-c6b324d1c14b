---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: apisix
  namespace: argocd
spec:
  destination:
    namespace: sansheng
    server: https://kubernetes.default.svc
  project: sansheng
  source:
    chart: apisix
    helm:
      values: |
        admin:
          allow:
            ipList:
              - ***********/22
              - *********/16
              - *********/16
              - 127.0.0.1/24
              - **********/24
          credentials:
            admin: qYm5AW0kK4FTFqlA
            viewer: 812k1WOnQyroYpmi
        apisix:
          enableIPv6: false
          replicaCount: 2
        dashboard:
          enabled: false
        discovery:
          enabled: true
          kubernetes:
            client:
              token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
            namespace_selector:
              not_equal: default
        etcd:
          auth:
            rbac:
              create: true
              password: lLiXI6I1R73vjtfq
              user: root
          enabled: false
          host:
            - http://etcd:2379
        log:
          errorLog: /dev/stdout
          errorLogLevel: warn
        plugins:
          - api-breaker
          - authz-casbin
          - authz-keycloak
          - basic-auth
          - batch-requests
          - consumer-restriction
          - cors
          - echo
          - ext-plugin-post-req
          - ext-plugin-pre-req
          - fault-injection
          - grpc-transcode
          - gzip
          - hmac-auth
          - http-logger
          - ip-restriction
          - jwt-auth
          - kafka-logger
          - key-auth
          - limit-conn
          - limit-count
          - limit-req
          - node-status
          - openid-connect
          - prometheus
          - proxy-cache
          - proxy-mirror
          - proxy-rewrite
          - real-ip
          - redirect
          - referer-restriction
          - request-id
          - request-validation
          - response-rewrite
          - server-info
          - serverless-post-function
          - serverless-pre-function
          - sls-logger
          - syslog
          - tcp-logger
          - traffic-split
          - ua-restriction
          - udp-logger
          - uri-blocker
          - wolf-rbac
          - zipkin
        serviceMonitor:
          enabled: true
    repoURL: https://artifactory.rp.konvery.work/artifactory/helm
    # repoURL: https://charts.apiseven.com
    targetRevision: 0.10.16
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: apisix-dashboard
  namespace: argocd
spec:
  destination:
    namespace: sansheng
    server: https://kubernetes.default.svc
  project: sansheng
  source:
    chart: apisix-dashboard
    helm:
      values: |
        config:
          authentication:
            users:
              - password: qYm5AW0kK4FTFqlA
                username: admin
          conf:
            etcd:
              endpoints:
                - http://etcd:2379
              password: lLiXI6I1R73vjtfq
              username: root
    repoURL: https://charts.apiseven.com
    targetRevision: 0.6.1
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
