apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: etcd
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: sansheng
  destination:
    namespace: sansheng
    server: https://kubernetes.default.svc
  source:
    repoURL: https://charts.bitnami.com/bitnami
    targetRevision: 8.5.5
    chart: etcd
    helm:
      values: |
        auth:
          rbac:
            allowNoneAuthentication: false
            enable: true
            rootPassword: lLiXI6I1R73vjtfq
          token:
            type: simple
        autoCompactionMode: periodic
        autoCompactionRetention: 10m
        # extraEnvVars:
        #   - name: ETCD_HEARTBEAT_INTERVAL
        #     value: '500'
        initialClusterState: existing
        logLevel: debug
        removeMemberOnContainerTermination: false
        replicaCount: 3
        updateStrategy:
          type: OnDelete
  syncPolicy:
    automated:
      selfHeal: true
      prune: true
    syncOptions:
    - CreateNamespace=true
