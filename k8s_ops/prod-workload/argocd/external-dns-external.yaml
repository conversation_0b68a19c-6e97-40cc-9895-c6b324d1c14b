apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: external-dns-external
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: infra
  destination:
    namespace: infra-public
    server: 'https://kubernetes.default.svc'
  source:
    repoURL: 'https://charts.bitnami.com/bitnami'
    targetRevision: '6.8.2'
    chart: external-dns
    helm:
      values: |
        domainFilters:
        - d.konvery.com
        sources:
          - service
          - ingress
        aws:
          region: cn-northwest-1
          zoneType: public
        serviceAccount:
          name: external-dns-external
          annotations:
            eks.amazonaws.com/role-arn: arn:aws-cn:iam::************:role/prod-workload-externaldns
        provider: aws
        annotationFilter: external-dns-annotation in (ingress-nginx-external)
        policy: sync
        txtOwnerId: prod-workload-external
  syncPolicy:
    automated:
      selfHeal: true
      prune: true
    syncOptions:
    - CreateNamespace=true