---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: keto
  namespace: argocd
spec:
  destination:
    namespace: sansheng
    server: https://kubernetes.default.svc
  project: sansheng
  source:
    chart: keto
    helm:
      parameters:
        - name: deployment.autoscaling.minReplicas
          value: "2"
      values: |
        deployment:
          extraEnv:
            - name: SQA_OPT_OUT
              value: "true"
            - name: NAMESPACES_EXPERIMENTAL_STRICT_MODE
              value: "true"
        keto:
          automigration:
            enabled: true
          config:
            namespaces:
              experimental_strict_mode: true
              location: file:///home/<USER>/namespaces.keto.ts
            serve:
              metrics:
                port: 4468
              read:
                port: 4466
              write:
                port: 4467
        secret:
          enabled: false
        service:
          metrics:
            enabled: true
    repoURL: https://artifactory.rp.konvery.work/artifactory/helm
    targetRevision: 0.1.8
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
