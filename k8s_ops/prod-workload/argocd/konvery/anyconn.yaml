---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: sansheng-anyconn2
  namespace: argocd
spec:
  destination:
    namespace: sansheng
    server: https://kubernetes.default.svc
  project: sansheng
  source:
    chart: sansheng-unicorn
    helm:
      parameters:
        - name: database.DATABASE
          value: anyconn
        - name: service.httpPort
          value: '8050'
        - name: service.grpcPort
          value: '8051'
        - name: envVars.rpc\.services\.iam\.addr
          value: iam:8001
        - name: envVars.rpc\.services\.anno\.addr
          value: 'anno:8011'
        - name: envVars.temporal\.addr
          value: temporal-frontend:7233
        - name: envVars.custsys\.customers\.dazhuo\.services\.filemanager\.endpoint
          value: https://zdrive-bigdata.mychery.com/operation/operationManager/token/auth
        - name: envVars.custsys\.customers\.dazhuo\.services\.filemanager\.access_key
          value: KonveryAnnotation
        - name: envVars.custsys\.customers\.dazhuo\.services\.filemanager\.secret_key
          value: K0xKlB06
        - name: envVars.custsys\.customers\.dazhuo\.services\.filemanager\.concurrency
          value: '10'
        - name: envVars.custsys\.customers\.dazhuo\.services\.filemanager\.extra\.check_url
          value: https://zdrive-bigdata.mychery.com/manager/dataManager/task/send/label/labelingValidation
        - name: envVars.custsys\.customers\.testin\.services\.default\.access_key
          value: fdcf7c3c827423196a51eb3bcb833190b2bb
        - name: envVars.custsys\.customers\.baidu\.services\.default\.access_key
          value: ALTAKzwokjOoXBJbYJr7Yi2XOi
        - name: envVars.custsys\.customers\.baidu\.services\.default\.secret_key
          value: 43b40417ef39410886adf963ede6abdb
        - name: envVars.lark\.app_id
          value: ********************
        - name: envVars.lark\.app_sec
          value: 9ajmjQ9M01e2xTRyiU5dtezoupoIwVzq
        - name: envVars.lark\.channels\.dazhuo\.receive_id
          value: oc_d3ed0f727e655040e3681ac404da150e
        - name: envVars.lark\.channels\.whookevt\.receive_id
          value: oc_36bc66ca9c19714ecc552f96d3e3ec20
        - name: envVars.lark\.channels\.baidu\.receive_id
          value: oc_d3ed0f727e655040e3681ac404da150e
        - name: persistence.enabled
          value: 'true'
        - name: persistence.size
          value: 500Gi
        - name: envVars.file_server\.bucket
          value: prod-workload-sansheng
        - name: envVars.file_server\.public_bucket
          value: konvery-images-public
        - name: envVars.file_server\.cloudfront\.enabled
          value: 'true'
        - name: envVars.file_server\.cloudfront\.distributions\.dist1\.origin
          value: s3://konvery-images-public
        - name: envVars.file_server\.cloudfront\.distributions\.dist1\.url_prefix
          value: https://s3pdip.d.konvery.com
        - name: envVars.file_server\.cloudfront\.distributions\.dist1\.public
          value: 'true'
        - name: envVars.file_server\.cloudfront\.distributions\.dist2\.origin
          value: s3://prod-workload-sansheng
        - name: envVars.file_server\.cloudfront\.distributions\.dist2\.url_prefix
          value: https://s3pdss.d.konvery.com
        - name: envVars.server\.http\.cors_origins
          value: null,https://admin.d.konvery.com,https://label.d.konvery.com,https://tars.d.konvery.com
        - name: envVars.DAZHUO_SKIP_TLS_VERIFY
          value: 'true'
        - name: envVars.DAZHUO_NOTIFY_ANNOS
          value: 'false'
        - name: envVars.BOSS_TIMESTAMP_PAST
          value: '30758400'
        - name: envVars.ANYCONN_WHOOKEVT_NO_AUTO_COMMENT
          value: 'false'
        - name: envVars.ANYCONN_WHOOKEVT_AUTO_REJECT_JOB
          value: 'false'
        - name: autoscaling.minReplicas
          value: '1'
        - name: autoscaling.maxReplicas
          value: '1'
        - name: envVars.otel\.log\.level-
          value: debug
        - name: fullnameOverride
          value: anyconn
        - name: nameOverride
          value: anyconn
      values: |
        image:
          svcCommand: ["/app/unicorn", "--conf", "/data/conf/anyconn.yaml", "anyconn", "serve"]
          migrateCommand: ["/app/unicorn", "--conf", "/data/conf/anyconn.yaml", "anyconn", "migrate", "autoup"]
        envVars.mq\.producer\.provider: "unspecified"
        envVars.mq\.consumer\.provider: "unspecified"
        serviceAccount:
          create: false
          name: sansheng-annofeed # sansheng-anyconn
          annotations:
            eks.amazonaws.com/role-arn: arn:aws-cn:iam::************:role/prod-workload-sansheng
        hostName: anyconn.d.konvery.com
        ingress:
          enabled: true
          className: "ingress-nginx-external"
          annotations:
            # nginx.ingress.kubernetes.io/ssl-passthrough: "true"
            external-dns-annotation: ingress-nginx-external
            nginx.ingress.kubernetes.io/proxy-body-size: 1024m
            nginx.ingress.kubernetes.io/enable-cors: "false"
          hosts:
            - host: anyconn.d.konvery.com
              paths:
                - path: /
                  pathType: ImplementationSpecific
          tls:
            - hosts:
              - anyconn.d.konvery.com
              secretName: wildcard-d-konvery-com
    repoURL: https://artifactory.rp.konvery.work/artifactory/helm
    targetRevision: v0.1.23
  syncPolicy:
    automated: {}
