---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: iam
  namespace: argocd
spec:
  destination:
    namespace: sansheng
    server: https://kubernetes.default.svc
  project: sansheng
  source:
    chart: iam
    helm:
      parameters:
        - name: envVars.danger_test\.fake_logins\.verification\.phone_regexp
          value: ^\\+04\\d{3}
        - name: envVars.danger_test\.fake_logins\.verification\.auth_code
          value: n5pSvvpV8dBpxNRVMOrGmIKa7u3LBP1p
        - name: envVars.danger_test\.fake_logins\.demo\.phone_regexp
          value: ^\\+01010([0-3]\\d|4\\d{3})$
        - name: envVars.danger_test\.fake_logins\.demo\.auth_code
          value: '123456'
        - name: envVars.danger_test\.fake_logins\.preview\.phone_regexp
          value: '^\\+02010[01]\\d$'
        - name: envVars.danger_test\.fake_logins\.preview\.auth_code
          value: 'UNDYvAQuld3gNUHqi76qcz312ck4hday'
        - name: envVars.danger_test\.fake_logins\.plan\.phone_regexp
          value: '^\\+0100010[1-5]\\d{4}$'
        - name: envVars.danger_test\.fake_logins\.plan\.auth_code
          value: 'utk6elYTtm3LHPeb'
        - name: envVars.alisms\.access_key
          value: LTAI5tDfFxa6g7dJAmCd4TED
        - name: envVars.alisms\.secret_key
          value: ******************************
        - name: envVars.jwt\.sign_key
          value: jxKFz3VZgyHTMLXxhXtMPpuLjWoWO9xM
        - name: envVars.server\.cookie_domain
          value: konvery.com
        - name: envVars.keto\.read_addr
          value: keto-read:80
        - name: envVars.keto\.write_addr
          value: keto-write:80
        - name: envVars.otel\.log\.level
          value: debug
        - name: autoscaling.minReplicas
          value: '2'
      values: |
        serviceAccount:
          annotations:
            eks.amazonaws.com/role-arn: arn:aws-cn:iam::************:role/prod-workload-sansheng
    repoURL: https://artifactory.rp.konvery.work/artifactory/helm
    targetRevision: 0.4.1
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
