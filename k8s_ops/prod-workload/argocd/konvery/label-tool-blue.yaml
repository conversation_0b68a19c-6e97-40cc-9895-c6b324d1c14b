---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: label-tool-blue
  namespace: argocd
spec:
  destination:
    namespace: sansheng
    server: https://kubernetes.default.svc
  project: sansheng
  source:
    chart: label-tool
    helm:
      parameters:
        - name: autoscaling.minReplicas
          value: '1'    
      values: |
        envVars:
          NGINX_ROOT: "/usr/share/nginx/html/prod"
          NGINX_ENVSUBST_OUTPUT_DIR: "/etc/nginx/konvery.conf.d"
    repoURL: https://artifactory.rp.konvery.work/artifactory/helm
    targetRevision: 0.4.*
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
