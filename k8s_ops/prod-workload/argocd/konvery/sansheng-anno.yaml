---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: sansheng-anno
  namespace: argocd
spec:
  destination:
    namespace: sansheng
    server: https://kubernetes.default.svc
  project: sansheng
  source:
    chart: sansheng-anno
    helm:
      parameters:
        - name: database.DATABASE
          value: sansheng-core
        - name: envVars.rpc\.iam\.addr
          value: iam:8001
        - name: envVars.rpc\.annout\.addr
          value: annout:8031
        - name: envVars.rpc\.annofeed\.addr
          value: annofeed:8021
        - name: envVars.temporal\.addr
          value: temporal-frontend:7233
        - name: envVars.mq\.producer\.topic
          value: "arn:aws-cn:sns:cn-northwest-1:************:prod-workload-sns-sansheng-anno"
        - name: envVars.mq\.sqs\.name
          value: prod-workload-sqs-sansheng-anno
        - name: envVars.object_storage\.s3\.bucket
          value: prod-workload-sansheng
        - name: envVars.object_storage\.s3\.public_bucket
          value: konvery-images-public
        - name: envVars.object_storage\.cloudfront\.enabled
          value: 'false'
        - name: envVars.object_storage\.cloudfront\.distributions\.dist1\.origin
          value: 's3://konvery-images-public'
        - name: envVars.object_storage\.cloudfront\.distributions\.dist1\.url_prefix
          value: 'https://s3pdip.d.konvery.com'
        - name: envVars.object_storage\.cloudfront\.distributions\.dist1\.public
          value: 'true'
        - name: envVars.object_storage\.cloudfront\.distributions\.dist2\.origin
          value: 's3://prod-workload-sansheng'
        - name: envVars.object_storage\.cloudfront\.distributions\.dist2\.url_prefix
          value: 'https://s3pdss.d.konvery.com'
        - name: envVars.otel\.log\.level
          value: debug
        - name: fullnameOverride
          value: anno
        - name: nameOverride
          value: anno
        - name: autoscaling.minReplicas
          value: '2'
      values: |
        serviceAccount:
          annotations:
            eks.amazonaws.com/role-arn: arn:aws-cn:iam::************:role/prod-workload-sansheng
    repoURL: https://artifactory.rp.konvery.work/artifactory/helm
    targetRevision: 0.4.3
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
