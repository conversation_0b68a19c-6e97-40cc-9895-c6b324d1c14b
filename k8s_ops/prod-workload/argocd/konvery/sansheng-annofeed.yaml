---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: sansheng-annofeed
  namespace: argocd
spec:
  destination:
    namespace: sansheng
    server: https://kubernetes.default.svc
  project: sansheng
  source:
    chart: sansheng-annofeed
    helm:
      parameters:
        - name: database.DATABASE
          value: sansheng-source
        - name: envVars.rpc\.iam\.addr
          value: iam:8001
        - name: envVars.rpc\.anno\.addr
          value: anno:8011
        - name: envVars.temporal\.addr
          value: temporal-frontend:7233
        - name: envVars.file_server\.bucket
          value: prod-workload-sansheng
        - name: envVars.file_server\.public_bucket
          value: konvery-images-public
        - name: envVars.file_server\.svc_url
          value: https://anno.d.konvery.com/annofeed/v1
        - name: envVars.file_server\.cloudfront\.enabled
          value: "true"
        - name: envVars.file_server\.cloudfront\.distributions\.dist1\.origin
          value: "s3://konvery-images-public"
        - name: envVars.file_server\.cloudfront\.distributions\.dist1\.url_prefix
          value: "https://s3pdip.d.konvery.com"
        - name: envVars.file_server\.cloudfront\.distributions\.dist1\.public
          value: "true"
        - name: envVars.file_server\.cloudfront\.distributions\.dist2\.origin
          value: "s3://prod-workload-sansheng"
        - name: envVars.file_server\.cloudfront\.distributions\.dist2\.url_prefix
          value: "https://s3pdss.d.konvery.com"
        - name: envVars.mq\.producer\.topic
          value: "arn:aws-cn:sns:cn-northwest-1:************:prod-workload-sns-sansheng-annofeed"
        - name: envVars.mq\.sqs\.name
          value: prod-workload-sqs-sansheng-annofeed
        - name: envVars.otel\.log\.level
          value: debug
        - name: fullnameOverride
          value: annofeed
        - name: nameOverride
          value: annofeed
        - name: autoscaling.minReplicas
          value: '2'
      values: |
        serviceAccount:
          annotations:
            eks.amazonaws.com/role-arn: arn:aws-cn:iam::************:role/prod-workload-sansheng
    repoURL: https://artifactory.rp.konvery.work/artifactory/helm
    targetRevision: 0.4.2
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
