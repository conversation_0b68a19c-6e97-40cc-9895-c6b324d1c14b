---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: sansheng-annout
  namespace: argocd
spec:
  destination:
    namespace: sansheng
    server: https://kubernetes.default.svc
  project: sansheng
  source:
    chart: sansheng-annout
    helm:
      parameters:
        - name: database.DATABASE
          value: sansheng-export
        - name: envVars.rpc\.iam\.addr
          value: iam:8001
        - name: envVars.rpc\.anno\.addr
          value: anno:8011
        - name: envVars.rpc\.annofeed\.addr
          value: annofeed:8021
        - name: envVars.temporal\.addr
          value: temporal-frontend:7233
        - name: envVars.otel\.log\.level
          value: debug
        - name: fullnameOverride
          value: annout
        - name: nameOverride
          value: annout
        - name: autoscaling.minReplicas
          value: '2'
      values: |
        serviceAccount:
          annotations:
            eks.amazonaws.com/role-arn: arn:aws-cn:iam::************:role/prod-workload-sansheng
    repoURL: https://artifactory.rp.konvery.work/artifactory/helm
    targetRevision: 0.4.0
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
