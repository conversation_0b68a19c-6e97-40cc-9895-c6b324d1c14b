apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  annotations:
    volume.beta.kubernetes.io/storage-provisioner: ebs.csi.aws.com
    volume.kubernetes.io/selected-node: ip-10-67-71-8.cn-northwest-1.compute.internal
    volume.kubernetes.io/storage-provisioner: ebs.csi.aws.com
  finalizers:
    - kubernetes.io/pvc-protection
  labels:
    app: loki
    release: loki
  name: storage-loki-1
  namespace: monitoring
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: ebs-sc
  volumeMode: Filesystem

