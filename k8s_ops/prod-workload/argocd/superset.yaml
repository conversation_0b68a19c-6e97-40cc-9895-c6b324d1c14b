---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  name: superset
  namespace: argocd
spec:
  destination:
    namespace: kvy-ops
    server: https://kubernetes.default.svc
  project: kvy-ops
  source:
    chart: superset
    helm:
      values: |
        envFromSecret: superset-db-env
        ingress:
          annotations:
            external-dns-annotation: ingress-nginx-internal
          enabled: true
          hosts:
            - superset.rp.konvery.work
          ingressClassName: ingress-nginx-internal
          tls:
            - hosts:
                - superset.rp.konvery.work
              secretName: wildcard-rp-konvery-work
        postgresql:
          enabled: false
    repoURL: https://apache.github.io/superset
    targetRevision: 0.10.5
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
