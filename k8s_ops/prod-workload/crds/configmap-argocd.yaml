apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-cm
  namespace: argocd
  labels:
    app.kubernetes.io/name: argocd-cm
    app.kubernetes.io/part-of: argocd
data:
  resource.compareoptions: |
    ignoreAggregatedRoles: true
  url: https://argocd-workload.rp.konvery.work
  oidc.config: |
    name: Keycloak
    issuer: https://keycloak.rp.konvery.work/realms/konvery
    clientID: argocd-workload-rp
    clientSecret: FbcXGNdSJQpWjAP9CoHgDQ5w66afE4SX
    requestedScopes: ["openid", "profile", "email", "groups"]
---
apiVersion: v1
data:
  server.insecure: "false"
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/name: argocd-cmd-params-cm
    app.kubernetes.io/part-of: argocd
  name: argocd-cmd-params-cm
  namespace: argocd
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-rbac-cm
  namespace: argocd
  labels:
    app.kubernetes.io/name: argocd-rbac-cm
    app.kubernetes.io/part-of: argocd
data:
  policy.csv: |
    p, role:editor, applications, *, */*, allow
    p, role:editor, clusters, get, *, allow
    p, role:editor, repositories, get, *, allow
    p, role:editor, repositories, create, *, allow
    p, role:editor, repositories, update, *, allow
    p, role:editor, repositories, delete, *, allow
    p, role:editor, logs, get, *, allow
    p, role:editor, exec, create, */*, allow
    p, role:editor, projects, get, *, allow
    g, kvy-admin, role:admin
    g, kvy-editor, role:editor
    g, kvy-viewer, role:readonly
---
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/name: argocd-notifications-cm
    app.kubernetes.io/part-of: argocd
  name: argocd-notifications-cm
  namespace: argocd
data:
  service.webhook.feishu_khal_bot: |
    url: https://open.feishu.cn/open-apis/bot/v2/hook/ca1fc76b-7376-40ac-b922-8c71820d3cdc
  service.webhook.feishu_khal_sre_bot: |
    url: https://open.feishu.cn/open-apis/bot/v2/hook/d35f1ef5-d930-410a-920f-762873e77b3f
  subscriptions: |
    - recipients:
        - feishu_khal_bot
      triggers:
        - on-app-sync-status-change
    - recipients:
        - feishu_khal_sre_bot
      triggers:
        - on-app-unhealthy
  template.app-unhealthy-warning: |
    webhook:
      feishu_khal_sre_bot:
        method: POST
        body: |
          {
            "msg_type": "post",
            "content": {
              "post": {
                "zh_cn": {
                  "title": "WARNING",
                  "content": [
                  [{
                  "tag": "text",
                  "text": "生产环境 {{.app.metadata.name}} 当前状态为 {{.app.status.health.status}}."
                  }]
                ]
                }
              }
            }
          }
  template.app-sync-status: |
    webhook:
      feishu_khal_bot:
        method: POST
        body: |
          {
            "msg_type": "post",
            "content": {
              "post": {
                "zh_cn": {
                  "title": "生产环境 {{.app.metadata.name}} 服务版本变更",
                  "content": [[{
                  "tag": "text",
                  {{if eq .app.status.operationState.phase "Running"}} "text": " SYNC 进行中, 旧版本 {{.app.status.sync.revision}}, 目标版本 {{.app.spec.source.targetRevision}}."{{end}}
                  {{if eq .app.status.operationState.phase "Succeeded"}} "text": " SYNC 完成, 版本 {{.app.status.sync.revision}}."{{end}}
                  }]]
                }
              }
            }
          }
  trigger.on-app-sync-status-change: |
    - send:
        - app-sync-status
      when: app.status.operationState.phase in ['Running']
      oncePer: app.status.operationState.syncResult.revision
    - send:
        - app-sync-status
      when: app.status.operationState.phase in ['Succeeded'] and time.Now().Sub(time.Parse(app.status.operationState.startedAt)).Minutes() < 2
  trigger.on-app-unhealthy: |
    - send:
        - app-unhealthy-warning
      when: app.status.health.status not in ['Healthy']
