apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: keto
  namespace: sansheng
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: external-secrets-kubernetes
    kind: ClusterSecretStore
  target:
    name: keto
    creationPolicy: Owner
    template: 
      data:
        dsn: postgres://{{ .username | toString }}:{{ .password | toString }}@{{ .endpoint | toString }}:{{ .port | toString }}/keto
  dataFrom:
    - extract:
        key: keto-role-secret
