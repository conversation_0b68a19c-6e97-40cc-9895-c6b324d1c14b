apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: sansheng-iam-s3
  namespace: sansheng
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: external-secrets
    kind: ClusterSecretStore
  target:
    name: sansheng-iam-s3
    creationPolicy: Owner
    template:
      data:
        AWS_ACCESS_KEY: "{{ .username | toString }}"
        AWS_SECRET_KEY: "{{ .password | toString }}"
  dataFrom:
    - extract:
        key: sansheng-iam-s3
