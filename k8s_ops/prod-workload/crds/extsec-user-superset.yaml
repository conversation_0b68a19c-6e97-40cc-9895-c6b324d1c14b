apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: superset-db-env
  namespace: kvy-ops
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: external-secrets-kvyops
    kind: ClusterSecretStore
  target:
    name: superset-db-env
    creationPolicy: Owner
    template:
      data:
        DB_HOST: "{{ .endpoint | toString }}"
        DB_NAME: "superset"
        DB_PASS: "{{ .password | toString }}"
        DB_PORT: "{{ .port | int }}"
        DB_USER: "{{.username | toString }}"
        REDIS_HOST: "superset-redis-headless"
        REDIS_PORT: "{{ 6379 | int }}"
  dataFrom:
    - extract:
        key: superset-role-secret
