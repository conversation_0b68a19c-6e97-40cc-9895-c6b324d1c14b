---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: wildcard-d-konvery-com
  namespace: sansheng
spec:
  dataFrom:
    - extract:
        key: wildcard-d-konvery-com
  refreshInterval: 1h
  secretStoreRef:
    kind: ClusterSecretStore
    name: external-secrets
  target:
    creationPolicy: Owner
    name: wildcard-d-konvery-com
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: wildcard-d-konvery-com
  namespace: infra
spec:
  dataFrom:
    - extract:
        key: wildcard-d-konvery-com
  refreshInterval: 1h
  secretStoreRef:
    kind: ClusterSecretStore
    name: external-secrets
  target:
    creationPolicy: Owner
    name: wildcard-d-konvery-com
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: wildcard-d-konvery-com
  namespace: monitoring
spec:
  dataFrom:
    - extract:
        key: wildcard-d-konvery-com
  refreshInterval: 1h
  secretStoreRef:
    kind: ClusterSecretStore
    name: external-secrets
  target:
    creationPolicy: Owner
    name: wildcard-d-konvery-com