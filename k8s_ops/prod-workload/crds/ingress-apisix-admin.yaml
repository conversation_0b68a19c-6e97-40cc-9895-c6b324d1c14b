---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    # nginx.ingress.kubernetes.io/backend-protocol: HTTP
    # nginx.ingress.kubernetes.io/ssl-passthrough: "false"
    external-dns-annotation: ingress-nginx-external
  name: apisix-admin
  namespace: sansheng
spec:
  ingressClassName: ingress-nginx-external
  rules:
    - host: admin.d.konvery.com
      http:
        paths:
          - backend:
              service:
                name: apisix-gateway
                port:
                  name: apisix-gateway
            path: /
            pathType: Prefix
  tls:
    - hosts:
        - admin.d.konvery.com
      secretName: wildcard-d-konvery-com
