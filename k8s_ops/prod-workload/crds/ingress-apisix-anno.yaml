---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    external-dns-annotation: ingress-nginx-external
    # nginx.ingress.kubernetes.io/backend-protocol: HTTP
    # nginx.ingress.kubernetes.io/ssl-passthrough: "false"
    # nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: 4096m
  name: apisix-anno
  namespace: sansheng
spec:
  ingressClassName: ingress-nginx-external
  rules:
    - host: anno.d.konvery.com
      http:
        paths:
          - backend:
              service:
                name: apisix-gateway
                port:
                  name: apisix-gateway
            path: /
            pathType: Prefix
  tls:
    - hosts:
        - anno.d.konvery.com
      secretName: wildcard-d-konvery-com