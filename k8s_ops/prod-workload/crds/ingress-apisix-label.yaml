---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    # nginx.ingress.kubernetes.io/backend-protocol: HTTP
    # nginx.ingress.kubernetes.io/ssl-passthrough: "false"
    external-dns-annotation: ingress-nginx-external
  name: apisix-label
  namespace: sansheng
spec:
  ingressClassName: ingress-nginx-external
  rules:
    - host: label.d.konvery.com
      http:
        paths:
          - backend:
              service:
                name: apisix-gateway
                port:
                  name: apisix-gateway
            path: /
            pathType: Prefix
  tls:
    - hosts:
        - label.d.konvery.com
      secretName: wildcard-d-konvery-com
