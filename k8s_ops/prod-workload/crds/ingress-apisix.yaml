---
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    external-dns-annotation: ingress-nginx-internal
    # kubernetes.io/ingress.class: nginx
    # nginx.ingress.kubernetes.io/backend-protocol: HTTPS
    nginx.ingress.kubernetes.io/ssl-passthrough: 'true'
  name: apisix-dashboard
  namespace: sansheng
spec:
  ingressClassName: ingress-nginx-internal
  rules:
    - host: apisix-dashboard.rp.konvery.work
      http:
        paths:
          - backend:
              service:
                name: apisix-dashboard
                port:
                  name: http
            path: /
            pathType: Prefix
  tls:
    - hosts:
        - apisix-dashboard.rp.konvery.work
      secretName: wildcard-rp-konvery-work
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    external-dns-annotation: ingress-nginx-internal
    # kubernetes.io/ingress.class: nginx
    # nginx.ingress.kubernetes.io/backend-protocol: HTTPS
    nginx.ingress.kubernetes.io/ssl-passthrough: 'true'
  name: apisixadmin
  namespace: sansheng
spec:
  ingressClassName: ingress-nginx-internal
  rules:
    - host: apisix-admin.rp.konvery.work
      http:
        paths:
          - backend:
              service:
                name: apisix-admin
                port:
                  number: 9180
            path: /
            pathType: Prefix
  tls:
    - hosts:
        - apisix-admin.rp.konvery.work
      secretName: wildcard-rp-konvery-work