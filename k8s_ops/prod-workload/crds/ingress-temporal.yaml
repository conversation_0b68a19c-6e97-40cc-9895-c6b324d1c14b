apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    # kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/backend-protocol: HTTP
    # nginx.ingress.kubernetes.io/ssl-passthrough: "true"
    external-dns-annotation: ingress-nginx-internal
  name: temporal
  namespace: sansheng
spec:
  ingressClassName: ingress-nginx-internal
  rules:
  - host: temporal.rp.konvery.work
    http:
      paths:
      - backend:
          service: 
            name: temporal-web
            port: 
              number: 8080
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - temporal.rp.konvery.work
    secretName: wildcard-rp-konvery-work