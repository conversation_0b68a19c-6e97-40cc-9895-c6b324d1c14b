---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: temporal
  namespace: sansheng
rules:
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: temporal
  namespace: sansheng
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: temporal
subjects:
  - kind: ServiceAccount
    name: temporal
    namespace: sansheng
